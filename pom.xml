<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <packaging>pom</packaging>

    <groupId>com.quantum.infrastructure</groupId>
    <artifactId>backend-wl-packet-bff</artifactId>
    <version>1.0.0-SNAPSHOT</version>

    <name>backend-wl-packet-bff</name>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.quantum.infrastructure</groupId>
                <artifactId>backend-common-dependency</artifactId>
                <version>1.0.0-SNAPSHOT</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.quantum.infrastructure</groupId>
                <artifactId>backend-wl-packet-bff-main</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.quantum.infrastructure</groupId>
                <artifactId>backend-wl-packet-bff-biz</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.quantum.infrastructure</groupId>
                <artifactId>backend-wl-packet-bff-dao</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.quantum.infrastructure</groupId>
                <artifactId>backend-wl-packet-bff-remote</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.quantum.infrastructure</groupId>
                <artifactId>backend-wl-packet-bff-sdk</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.quantum.infrastructure</groupId>
                <artifactId>backend-common-starter</artifactId>
                <version>1.0.2-SNAPSHOT</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.quantum.infrastructure</groupId>
                <artifactId>backend-common-basedata-sdk</artifactId>
                <version>1.0.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.quantum.infrastructure</groupId>
                <artifactId>backend-common-application-sdk</artifactId>
                <version>1.0.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.quantum.infrastructure</groupId>
                <artifactId>backend-common-lessee-sdk</artifactId>
                <version>1.0.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.quantum.infrastructure</groupId>
                <artifactId>backend-common-user-sdk</artifactId>
                <version>1.0.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.quantum.infrastructure</groupId>
                <artifactId>backend-common-message-sdk</artifactId>
                <version>1.0.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.quantum.infrastructure</groupId>
                <artifactId>backend-wl-packet-sdk</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-starter-netflix-zuul</artifactId>
                <version>2.2.5.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-starter-netflix-ribbon</artifactId>
                <version>2.1.5.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-test</artifactId>
                <version>5.1.9.RELEASE</version>
            </dependency>

            <!-- tomcat  -->
            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-jasper</artifactId>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.tomcat</groupId>
                <artifactId>tomcat-servlet-api</artifactId>
                <version>7.0.42</version>
                <scope>provided</scope>
            </dependency>
            <!-- tomcat  end-->
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-archetype-plugin</artifactId>
                <version>3.0.0</version>
            </plugin>
        </plugins>
    </build>

    <distributionManagement>
        <snapshotRepository>
            <id>snapshots</id>
            <name>Snapshot</name>
            <url>http://192.168.100.210:8081/repository/maven-snapshots/</url>
        </snapshotRepository>
        <repository>
            <id>releases</id>
            <name>Releases</name>
            <url>http://192.168.100.210:8081/repository/maven-release/</url>
        </repository>
    </distributionManagement>
  <modules>
    <module>backend-wl-packet-bff-remote</module>
    <module>backend-wl-packet-bff-sdk</module>
    <module>backend-wl-packet-bff-dao</module>
    <module>backend-wl-packet-bff-biz</module>
    <module>backend-wl-packet-bff-main</module>
  </modules>
</project>