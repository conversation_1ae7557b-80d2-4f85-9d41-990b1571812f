package com.quantum.infrastructure.wl.packet.bff.sdk.facade.user;

import com.quantum.infrastructure.common.application.sdk.dto.ResourceDto;
import com.quantum.infrastructure.common.application.sdk.dto.ext.ResourceExt;
import com.quantum.infrastructure.common.util.protocol.Result;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

public interface BffResourceFacade {

    @RequestMapping(value = "/updateResource", method = RequestMethod.PUT)
    Result<Integer> updateResource(@RequestBody ResourceDto resourceDto);

    /**
     * 查询角色资源权限
     * @param roleId
     * @return
     */
    @RequestMapping(value = "/queryResourceAndRightsList", method = RequestMethod.GET)
    Result<List<ResourceExt>> queryResourceAndRightsList(String roleId);
}
