package com.quantum.infrastructure.wl.packet.bff.sdk.facade.nexSupplier;

import com.quantum.infrastructure.common.groups.Save;
import com.quantum.infrastructure.common.groups.Update;
import com.quantum.infrastructure.common.util.protocol.BasePageRequest;
import com.quantum.infrastructure.common.util.protocol.Result;
import com.quantum.infrastructure.common.util.protocol.dto.PageDto;
import com.quantum.infrastructure.wl.packet.sdk.domain.vo.request.NextSupplierQueryReqVO;
import com.quantum.infrastructure.wl.packet.sdk.dto.NextSupplierDto;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 */
public interface NextSupplierRestful {
    /**
     */
    @PostMapping("/queryNextSupplierForPage")
    Result<PageDto<NextSupplierDto>> queryNextSupplierForPage(@RequestBody BasePageRequest<NextSupplierQueryReqVO> request);


    /**
     * 新增附加费
     *
     */
    @PostMapping("/addNextSupplier")
    Result<String> addNextSupplier(@Validated(Save.class) @RequestBody NextSupplierDto nextSupplierDto);

    /**
     * 更新附加费
     *
     */
    @PutMapping("/updateNextSupplier")
    Result<Integer> updateNextSupplier(@Validated(Update.class) @RequestBody NextSupplierDto nextSupplierDto);

    /**
     * 删除附加费
     *
     */
    @DeleteMapping("/deleteNextSupplier")
    Result<Integer> deleteNextSupplier(@RequestParam(value = "id") String id);

    /**
     * 审核附加费
     *
     */
    @PutMapping("/updateStatus")
    Result<Integer> updateStatus(@RequestParam(value = "id") String id, @RequestParam("status") String status);

}
