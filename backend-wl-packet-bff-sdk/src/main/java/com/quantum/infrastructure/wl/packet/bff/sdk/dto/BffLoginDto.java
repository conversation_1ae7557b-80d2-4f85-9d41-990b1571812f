package com.quantum.infrastructure.wl.packet.bff.sdk.dto;

import com.quantum.infrastructure.common.application.sdk.dto.ResourceDto;
import com.quantum.infrastructure.common.lessee.sdk.dto.LesseeDto;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by liuzengrong on 2017/8/31.
 */
public class BffLoginDto {
    /**
     * sessionId
     */
    private String sessionId;

   //权限
    private List<String> rightValues;

    //资源菜单
    private List<ResourceDto> resourceDtos = new ArrayList<>();


    private List<String> roleIds = new ArrayList<>();

    //用户名
    private String userName;
    
    private String lastLogonTime;

    //租户信息
    private LesseeDto lessee;


    //用户id
	private String userId;
    
    /**
     * 是否需要加强校验
     */
    private Boolean isNeedVerifyCode;

    //渠道
	private String channel;


    /**
     * 租户组编码
     */
    private String lesseeGroupCode;


    /**
     * 租户logo URl
     */
    private String lesseeLogoUrl;

    private String cstId;


    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public List<String> getRightValues() {
        return rightValues;
    }

    public void setRightValues(List<String> rightValues) {
        this.rightValues = rightValues;
    }

    public List<ResourceDto> getResourceDtos() {
        return resourceDtos;
    }

    public void setResourceDtos(List<ResourceDto> resourceDtos) {
        this.resourceDtos = resourceDtos;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getLastLogonTime() {
        return lastLogonTime;
    }

    public void setLastLogonTime(String lastLogonTime) {
        this.lastLogonTime = lastLogonTime;
    }

    public LesseeDto getLessee() {
        return lessee;
    }

    public void setLessee(LesseeDto lessee) {
        this.lessee = lessee;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public Boolean getNeedVerifyCode() {
        return isNeedVerifyCode;
    }

    public void setNeedVerifyCode(Boolean needVerifyCode) {
        isNeedVerifyCode = needVerifyCode;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getLesseeGroupCode() {
        return lesseeGroupCode;
    }

    public void setLesseeGroupCode(String lesseeGroupCode) {
        this.lesseeGroupCode = lesseeGroupCode;
    }

    public String getLesseeLogoUrl() {
        return lesseeLogoUrl;
    }

    public void setLesseeLogoUrl(String lesseeLogoUrl) {
        this.lesseeLogoUrl = lesseeLogoUrl;
    }

    public List<String> getRoleIds() {
        return roleIds;
    }

    public void setRoleIds(List<String> roleIds) {
        this.roleIds = roleIds;
    }

    public String getCstId() {
        return cstId;
    }

    public void setCstId(String cstId) {
        this.cstId = cstId;
    }
}
