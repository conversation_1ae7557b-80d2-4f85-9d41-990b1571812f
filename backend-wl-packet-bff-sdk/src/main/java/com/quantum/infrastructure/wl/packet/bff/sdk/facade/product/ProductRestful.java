package com.quantum.infrastructure.wl.packet.bff.sdk.facade.product;

import com.quantum.infrastructure.common.groups.Save;
import com.quantum.infrastructure.common.groups.Update;
import com.quantum.infrastructure.common.util.protocol.BasePageRequest;
import com.quantum.infrastructure.common.util.protocol.Result;
import com.quantum.infrastructure.common.util.protocol.dto.PageDto;
import com.quantum.infrastructure.wl.packet.sdk.dto.ProductDto;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;



/**
 * @Author: liuquanli
 * @CreateTime: 2025-06-13
 */
public interface ProductRestful {
    @PostMapping("/addProduct")
    Result<Integer> addProduct(@Validated(Save.class) @RequestBody ProductDto productDto);

    @PutMapping("/updateProduct")
    Result<Integer> updateProduct(@Validated(Update.class)@RequestBody ProductDto productDto);

    @PostMapping("/listProductForPage")
    Result<PageDto<ProductDto>> listProductForPage(@RequestBody BasePageRequest<ProductDto> basePageRequest);
}
