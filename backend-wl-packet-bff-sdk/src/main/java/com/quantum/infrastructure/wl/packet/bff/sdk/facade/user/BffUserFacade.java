package com.quantum.infrastructure.wl.packet.bff.sdk.facade.user;

import com.quantum.infrastructure.common.user.sdk.dto.UserDto;
import com.quantum.infrastructure.common.util.protocol.Result;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

public interface BffUserFacade {


    @RequestMapping(value = "/getCurrentUserInfo", method = {RequestMethod.GET})
    Result<UserDto> getCurrentUserInfo();

    /**
     * 通过角色名称获列表
     *
     * @param roleName
     * @return
     */
    @GetMapping("/getListByAliasAndRoleName")
    Result<List<UserDto>> getListByAliasAndRoleName(@RequestParam(value = "roleName", required = true) String roleName);

    /**
     * 刷新角色菜单
     *
     * @param save   是否保存：1-是
     * @param roleId 角色id
     * @return Integer
     */
    @RequestMapping(value = "/refreshRoleResource", method = {RequestMethod.PATCH})
    Result<Integer> refreshRoleResource(@RequestParam(value = "save", required = false) String save,
                                        @RequestParam(value = "roleId", required = false) String roleId);
}
