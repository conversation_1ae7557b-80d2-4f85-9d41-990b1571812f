package com.quantum.infrastructure.wl.packet.bff.sdk.facade.user;

import com.quantum.infrastructure.common.auth.sdk.dto.LoginDto;
import com.quantum.infrastructure.common.auth.sdk.dto.SessionUserInfoDto;
import com.quantum.infrastructure.common.lessee.sdk.dto.LesseeDto;
import com.quantum.infrastructure.common.util.protocol.Result;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 认证
 *
 * @Author: Ron
 * @Date: 2019/8/24
 */
//@FeignClient(name = "backend-service-center-auth")
public interface AuthFacade {

    /**
     * 登录
     *
     * @param loginDto
     * @return
     */
    @RequestMapping("/login")
    Result<Object> login(LoginDto loginDto);


    /**
     * 更改用户账号状态
     * @param id
     * @param status
     * @return
     */
    @RequestMapping(value = "/userStatus", method = RequestMethod.PUT)
    Result<Integer> userStatus(String id, String status);

    /**
     * 查询登录账号多租户列表
     */
    @GetMapping("/queryMulLessee")
    Result<List<LesseeDto>> queryMulLessee(@RequestParam(value = "loginId") String loginId, @RequestParam(value = "appCode") String appCode);

    /**
     * 获取登录用户信息
     *
     * @param token 令牌
     * @return SessionUserInfoDto
     */
    @GetMapping("/getSessionUserInfo")
    Result<SessionUserInfoDto> getSessionUserInfo(String token);

    /**
     * 更新用户信息
     *
     * @param roleId 角色id
     * @return Result<String>
     */
    @PutMapping(value = "/switchCurrentRole")
    Result<String> switchCurrentRole(@RequestParam(value = "roleId") String roleId, @RequestParam(value = "token") String token);
}
