package com.quantum.infrastructure.wl.packet.bff.sdk.facade.customer;

import com.quantum.infrastructure.common.groups.Save;
import com.quantum.infrastructure.common.groups.Update;
import com.quantum.infrastructure.common.util.protocol.BasePageRequest;
import com.quantum.infrastructure.common.util.protocol.Result;
import com.quantum.infrastructure.common.util.protocol.dto.PageDto;
import com.quantum.infrastructure.wl.packet.sdk.dto.CustomerDto;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @Author: liuquanli
 * @CreateTime: 2025-06-13
 */
public interface CustomerRestful {
    @PostMapping("/addCustomer")
    Result<Integer> addCustomer(@Validated(Save.class) @RequestBody CustomerDto customerDto);

    @PutMapping("/updateCustomer")
    Result<Integer> updateCustomer(@Validated(Update.class) @RequestBody CustomerDto customerDto);

    @PostMapping("/listCustomerForPage")
    Result<PageDto<CustomerDto>> listCustomerForPage(@RequestBody BasePageRequest<CustomerDto> basePageRequest);


}
