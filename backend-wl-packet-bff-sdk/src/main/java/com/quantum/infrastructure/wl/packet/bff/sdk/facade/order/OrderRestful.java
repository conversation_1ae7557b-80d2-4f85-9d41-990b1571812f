package com.quantum.infrastructure.wl.packet.bff.sdk.facade.order;

import com.quantum.infrastructure.common.util.protocol.BasePageRequest;
import com.quantum.infrastructure.common.util.protocol.Result;
import com.quantum.infrastructure.common.util.protocol.dto.PageDto;
import com.quantum.infrastructure.wl.packet.sdk.dto.OrderInfoDto;
import com.quantum.infrastructure.wl.packet.sdk.vo.request.OrderInfoReqVO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/6/17 14:28
 */
public interface OrderRestful {
    @PostMapping("/createOrder")
    Result<String> createOrder(@RequestBody OrderInfoDto request);
    @PostMapping("/listForPage")
    Result<PageDto<OrderInfoDto>> listForPage(@RequestBody BasePageRequest<OrderInfoReqVO> request);
    @PutMapping("/updateOrderStatus")
    Result<Integer> updateOrderStatus(@RequestBody OrderInfoDto request);
    @PutMapping("/singleReceive")
    Result<Integer> singleReceive(@RequestBody OrderInfoDto request);
    @PutMapping("/singleShipment")
    Result<Integer> singleShipment(@RequestBody OrderInfoDto request);
}
