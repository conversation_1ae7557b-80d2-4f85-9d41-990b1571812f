<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.quantum.infrastructure</groupId>
        <artifactId>backend-wl-packet-bff</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <packaging>jar</packaging>

    <artifactId>backend-wl-packet-bff-sdk</artifactId>
    <name>backend-wl-packet-bff-sdk</name>

    <dependencies>
        <dependency>
            <groupId>com.quantum.infrastructure</groupId>
            <artifactId>backend-common-framework-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.quantum.infrastructure</groupId>
            <artifactId>backend-common-basedata-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.quantum.infrastructure</groupId>
            <artifactId>backend-common-application-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.quantum.infrastructure</groupId>
            <artifactId>backend-common-lessee-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.quantum.infrastructure</groupId>
            <artifactId>backend-common-user-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.quantum.infrastructure</groupId>
            <artifactId>backend-wl-packet-sdk</artifactId>
        </dependency>
    </dependencies>
</project>
