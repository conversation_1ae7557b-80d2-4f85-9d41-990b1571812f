package com.quantum.infrastructure.wl.packet.bff.biz.service.impl.user;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.quantum.infrastructure.common.util.exception.BusinessException;
import com.quantum.infrastructure.common.util.protocol.CacheKey;
import com.quantum.infrastructure.wl.packet.bff.biz.service.user.SmsVerifyCodeService;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date: 2022/03/08
 * @Descrition
 */
@Service
public class SmsVerifyCodeServiceImpl implements SmsVerifyCodeService {

    @Resource
    private RedisTemplate<String, Serializable> redisTemplate;

    @Override
    public void validateSmsVerifyCode(String mobile, String code) {
        String redisKey = getSmsVerifyCodeCacheKey(mobile);
        String cacheCode = (String) redisTemplate.opsForValue().get(redisKey);
        boolean isProd = SpringUtil.getActiveProfile().contains("prod");
        boolean isDebug =  "134679".equals(code);
        if (StrUtil.isEmpty(cacheCode) && !isDebug) {
            throw new BusinessException("短信验证码已失效，请重新获取");
        }
        boolean isValidate = StrUtil.equals(code, cacheCode);
        if (!isValidate && !isDebug) {
            throw new BusinessException("请输入正确的短信验证码");
        }
        redisTemplate.delete(redisKey);
    }

    private String getSmsVerifyCodeCacheKey(String mobile) {
        return CacheKey.APP_MAIN.concat("sms_verify_code_").concat(mobile);
    }
}
