package com.quantum.infrastructure.wl.packet.bff.biz.facade.user;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.quantum.infrastructure.common.user.sdk.dto.ResourceDto;
import com.quantum.infrastructure.common.user.sdk.dto.RoleDto;
import com.quantum.infrastructure.common.user.sdk.dto.UserDto;
import com.quantum.infrastructure.common.user.sdk.vo.RoleResourcesMappingVo;
import com.quantum.infrastructure.common.util.protocol.IsDeleteStatusEnums;
import com.quantum.infrastructure.common.util.protocol.Result;
import com.quantum.infrastructure.common.util.protocol.YNStatusEnums;
import com.quantum.infrastructure.common.util.utils.WebUtils;
import com.quantum.infrastructure.wl.packet.bff.remote.user.ResourceRemoteService;
import com.quantum.infrastructure.wl.packet.bff.remote.user.RoleRemoteService;
import com.quantum.infrastructure.wl.packet.bff.remote.user.RoleResourceMappingRemote;
import com.quantum.infrastructure.wl.packet.bff.remote.user.UserRemoteService;
import com.quantum.infrastructure.wl.packet.bff.sdk.facade.user.BffUserFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StopWatch;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @ProjectName: backend-lzwz-bff
 * @Package: com.quantum.infrastructure.lzwz.bff.biz.facade.user
 * @ClassName: UserFacadeImpl
 * @Description: java类作用描述
 * @Author: 权力
 * @CreateDate: 2021/5/8 10:59
 * @UpdateUser: 更新者
 * @UpdateDate: 2021/5/8 10:59
 * @UpdateRemark: 更新说明
 * @Version: 1.0
 */
@RestController
@RequestMapping("user")
@Slf4j
public class UserFacadeImpl implements BffUserFacade {


    @Autowired
    private UserRemoteService userRemoteService;

    @Resource
    private RoleRemoteService roleRemoteService;

    @Resource
    private RoleResourceMappingRemote roleResourceMappingRemote;

    @Resource
    private ResourceRemoteService resourceRemoteService;

    @Override
    public Result<UserDto> getCurrentUserInfo() {
        Result<UserDto> result = new Result<>();
        String userId = WebUtils.getSessionInfo().getUserId();
        UserDto userDto = userRemoteService.getUser(userId).getData();
        result.setData(userDto);
        return result;
    }

    /**
     * 通过角色名称获列表
     *
     * @param roleName
     * @return
     */
    @Override
    public Result<List<UserDto>> getListByAliasAndRoleName(String roleName) {
        return userRemoteService.getListByAliasAndRoleName(null, roleName, null, null);
    }

    @Override
    public Result<Integer> refreshRoleResource(String save, String roleId) {
        Result<Integer> result = new Result<>();
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        final String appCode = WebUtils.getSessionInfo().getAppCode();
        final String lesseeId = WebUtils.getSessionInfo().getLesseeId();
        log.info("[refreshRoleResource begin] [appCode:{} lesseeId:{} save:{}]", appCode, lesseeId, save);

        // 获取租户下的所有角色
        RoleDto roleCondition = new RoleDto();
        roleCondition.setAppCode(appCode);
        roleCondition.setLesseeId(lesseeId);
        roleCondition.setIsDelete(IsDeleteStatusEnums.NORMAL.getValue());
        List<RoleDto> roleDTOList = roleRemoteService.queryList(roleCondition, null).getData();
        log.info("[refreshRoleResource begin] [roleDtoList size:{}]", roleDTOList != null ? roleDTOList.size() : 0);

        if (CollectionUtils.isNotEmpty(roleDTOList)) {
            com.quantum.infrastructure.common.application.sdk.dto.ResourceDto resourceCondition1 = new com.quantum.infrastructure.common.application.sdk.dto.ResourceDto();
            for (RoleDto roleDTO : roleDTOList) {
                if (StrUtil.isNotBlank(roleId) && !roleId.equals(roleDTO.getId())) {
                    continue;
                }

                // 查询角色资源
                List<ResourceDto> roleResourceDTOList = roleResourceMappingRemote.queryResourceListByRoleIdV2(roleDTO.getId(), lesseeId, appCode).getData();
                if (CollUtil.isNotEmpty(roleResourceDTOList)) {
                    List<String> newResourceIds = new ArrayList<>();

                    //  提取父子对象到一个List
                    roleResourceDTOList = flatten(roleResourceDTOList);

                    // 包含所有父子级资源id
                    List<String> existResourceIds = roleResourceDTOList.stream().map(ResourceDto::getId).collect(Collectors.toList());
                    Map<String, ResourceDto> existResourceMap = roleResourceDTOList.stream().collect(Collectors.toMap(ResourceDto::getId, res -> res));

                    log.info("[refreshRoleResource] [roleId:{} original resourceIds :{}]", roleDTO.getId(), existResourceIds.size());

                    for (String resourceId : existResourceIds) {
                        // 排除一级资源
                        if (existResourceMap.get(resourceId).getParentId().equals("0")) {
                            continue;
                        }

                        // 找子级（二级资源是否有新增的子级，此处存在一种情况是将原本未配置的二级资源添加为子级）
                        resourceCondition1.setIsDelete(IsDeleteStatusEnums.NORMAL.getValue());
                        resourceCondition1.setParentId(resourceId);
                        resourceCondition1.setStatus("ENABLE");
                        List<com.quantum.infrastructure.common.application.sdk.dto.ResourceDto> childsList = resourceRemoteService.queryList(resourceCondition1, null).getData();
                        if (CollUtil.isNotEmpty(childsList)) {
                            // 求差集（childsList-existResourceIds）
                            List<com.quantum.infrastructure.common.application.sdk.dto.ResourceDto> diffResourceList = childsList.stream()
                                    .filter(child -> !existResourceIds.contains(child.getId()))
                                    .collect(Collectors.toList());
                            if (CollUtil.isNotEmpty(diffResourceList)) {
                                newResourceIds.addAll(diffResourceList.stream()
                                        .map(com.quantum.infrastructure.common.application.sdk.dto.ResourceDto::getId)
                                        .collect(Collectors.toList()));
                            }
                        }

                        // 找父级（如果资源的父级不存在，说明当前资源是原本是二级，现变更为角色未配置的二级资源的子级，针对这种情况，要将其父级一并添加到角色资源中，排除一级资源）
                        this.addParentResource(resourceId, existResourceMap, existResourceIds, newResourceIds);
                    }

                    // 批量保存当前角色的资源
                    if (CollUtil.isNotEmpty(newResourceIds)) {
                        List<String> saveResourceIds = new ArrayList<>(newResourceIds);
                        saveResourceIds.addAll(existResourceIds);

                        // 去重
                        saveResourceIds = saveResourceIds.stream().distinct().collect(Collectors.toList());

                        log.info("[refreshRoleResource] [save roleId:{} saveResourceIds size :{}]", roleDTO.getId(), saveResourceIds.size());

                        if (StrUtil.isAllNotBlank(save) && YNStatusEnums.YES_NUM.getValue().equals(save)) {
                            RoleResourcesMappingVo saveVo = new RoleResourcesMappingVo();
                            saveVo.setAppCode(appCode);
                            saveVo.setRoleId(roleDTO.getId());
                            saveVo.setResourceIds(saveResourceIds);
                            // 此接口会先删除所有资源再新增，因此需要把初始资源一起保存
                            roleResourceMappingRemote.batchSave(saveVo);
                        }
                    }

                }
            }
        }

        stopWatch.stop();
        log.info("[refreshRoleResource end] [totalDuration:{} seconds]", stopWatch.getTotalTimeSeconds());

        return result;
    }

    /**
     * 提取父子对象到一个List
     *
     * @param resourceDTOList ResourceDto
     * @return ResourceDto
     */
    private List<ResourceDto> flatten(List<ResourceDto> resourceDTOList) {
        List<ResourceDto> result = new ArrayList<>();

        // 递归处理对象树
        resourceDTOList.forEach(p -> {
            result.add(p); // 添加父对象

            if (CollUtil.isNotEmpty(p.getChildsList())) {
                result.addAll(flatten(p.getChildsList())); // 递归添加子对象
            }
        });

        return result;
    }

    /**
     * 获取未配置的父级资源
     *
     * @param resourceId       资源id
     * @param existResourceMap 已存在的资源
     * @param existResourceIds 已存在的资源id
     * @return 资源id
     */
    private void addParentResource(String resourceId, Map<String, ResourceDto> existResourceMap, List<String> existResourceIds, List<String> newResourceList) {
        ResourceDto resourceDto = existResourceMap.get(resourceId);
        if (ObjectUtil.isNotNull(resourceDto) && StrUtil.isAllNotBlank(resourceDto.getTreePath(), resourceDto.getParentId())
                && !YNStatusEnums.NO_NUM.getValue().equals(resourceDto.getParentId())) {
            String[] treePathArray = resourceDto.getTreePath().split(",");
            if (ArrayUtil.isNotEmpty(treePathArray)) {
                for (String resId : treePathArray) {
                    if (!existResourceIds.contains(resId)) {
                        newResourceList.add(resId);
                    }
                }
            }
        }
    }

}
