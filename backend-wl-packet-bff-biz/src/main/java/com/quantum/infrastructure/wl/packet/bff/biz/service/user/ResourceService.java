package com.quantum.infrastructure.wl.packet.bff.biz.service.user;

import com.quantum.infrastructure.common.application.sdk.dto.ext.ResourceExt;
import com.quantum.infrastructure.common.util.protocol.Result;

import java.util.List;

public interface ResourceService {

    /**
     * 获取角色资源和权限列表
     * @param roleId   角色id
     * @param lesseeId 租户id
     * @param appCode  应用编码
     * @return
     */
    Result<List<ResourceExt>> queryResourceAndRightsList(String roleId, String lesseeId, String appCode);


    
}
