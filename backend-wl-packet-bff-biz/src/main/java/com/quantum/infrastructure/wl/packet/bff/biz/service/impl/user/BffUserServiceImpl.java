package com.quantum.infrastructure.wl.packet.bff.biz.service.impl.user;

import com.quantum.infrastructure.common.user.sdk.dto.RoleDto;
import com.quantum.infrastructure.common.user.sdk.dto.UserRoleMappingDto;
import com.quantum.infrastructure.common.util.protocol.IsDeleteStatusEnums;
import com.quantum.infrastructure.common.util.protocol.Result;
import com.quantum.infrastructure.common.util.utils.CollectionHelpUtils;
import com.quantum.infrastructure.common.util.utils.WebUtils;
import com.quantum.infrastructure.wl.packet.bff.biz.service.user.BffUserService;
import com.quantum.infrastructure.wl.packet.bff.remote.user.RoleRemoteService;
import com.quantum.infrastructure.wl.packet.bff.remote.user.UserRoleRemoteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Classname BffUserServiceImpl
 * @Date 2022/2/15 6:07 下午
 * @Created by miracle
 */
@Service
public class BffUserServiceImpl implements BffUserService {

    @Autowired
    private UserRoleRemoteService userRoleRemoteService;

    @Autowired
    private RoleRemoteService roleRemoteService;

    @Override
    public Result<List<RoleDto>> getRoleIdsByUserId(String userId) {
        Result<List<RoleDto>> result = new Result<>();
        UserRoleMappingDto condition = new UserRoleMappingDto();
        condition.setUserId(userId);
        condition.setIsDelete(IsDeleteStatusEnums.NORMAL.getValue());
        condition.setLesseeId(WebUtils.getSessionInfo().getLesseeId());
        List<UserRoleMappingDto> mappers = userRoleRemoteService.queryList(condition, null).getData();

        if (!CollectionUtils.isEmpty(mappers)) {
            List<String> roleIds = mappers.stream().map(s -> s.getRoleId()).collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(roleIds)) {
                List<RoleDto> roleDtos = roleRemoteService.queryListByIds(CollectionHelpUtils.listToArray(roleIds, String.class), null).getData();
                result.setData(roleDtos);
            }
        }

        return result;
    }
}
