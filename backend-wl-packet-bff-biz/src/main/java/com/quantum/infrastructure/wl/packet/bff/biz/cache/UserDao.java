package com.quantum.infrastructure.wl.packet.bff.biz.cache;


import com.alibaba.fastjson.JSON;
import com.quantum.infrastructure.common.application.sdk.dto.ResourceDto;
import com.quantum.infrastructure.common.util.protocol.RedisKeyEnums;
import com.quantum.infrastructure.common.util.protocol.dto.SessionDto;
import com.quantum.infrastructure.common.util.utils.RedisUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by l<PERSON><PERSON><PERSON>ng on 2017/8/24.
 */
@Repository
public class UserDao {


    @Autowired
    private RedisUtils redisUtils;


    /**
     * session超时时间，单位：秒，默认60分钟
     */
//    private int sessionTimeout = 60 * 60;

    /**
     * 图形验证码超时时间,单位：秒，默认15分钟
     */
    private int verifyCodeTimeOut = 15 * 60;


    /**
     * 将用户session信息存入缓存
     *
     * @param user
     * @param menus
     * @param rightsValues
     */
    public void setSessionInfo(String sessionId, SessionDto user, List<ResourceDto> menus, List<String> rightsValues,
                               List<String> apisValues, List<String> oriApisValues, long sessionTimeout) {
        if (user != null) {
            redisUtils.expire(RedisKeyEnums.PACKET_SESSION_USER.getValue() + sessionId, 0);
            redisUtils.set(RedisKeyEnums.PACKET_SESSION_USER.getValue() + sessionId, JSON.toJSONString(user), sessionTimeout);
        }
        if (rightsValues != null) {
            redisUtils.expire(RedisKeyEnums.PACKET_SESSION_RIGHTS.getValue() + user.getUserId(), 0);
            redisUtils.rightPushAll(RedisKeyEnums.PACKET_SESSION_RIGHTS.getValue() + user.getUserId(), rightsValues);
            redisUtils.expire(RedisKeyEnums.PACKET_SESSION_RIGHTS.getValue() + user.getUserId(), sessionTimeout);

        }

        if (apisValues != null) {
            redisUtils.expire(RedisKeyEnums.PACKET_SESSION_APIS.getValue() + user.getUserId(), 0);
            redisUtils.rightPushAll(RedisKeyEnums.PACKET_SESSION_APIS.getValue() + user.getUserId(), apisValues);
            redisUtils.expire(RedisKeyEnums.PACKET_SESSION_APIS.getValue() + user.getUserId(), sessionTimeout);

        }

        if (oriApisValues != null) {
            redisUtils.expire(RedisKeyEnums.PACKET_SESSION_ORGANIZATION_APIS.getValue() + user.getUserId(), 0);
            redisUtils.rightPushAll(RedisKeyEnums.PACKET_SESSION_ORGANIZATION_APIS.getValue() + user.getUserId(), oriApisValues);
            redisUtils.expire(RedisKeyEnums.PACKET_SESSION_ORGANIZATION_APIS.getValue() + user.getUserId(), sessionTimeout);

        }else {
            // 如果oriApisValues为null，删除key
            redisUtils.expire(RedisKeyEnums.PACKET_SESSION_ORGANIZATION_APIS.getValue() + user.getUserId(), 0);
        }


        if (menus != null) {
            redisUtils.expire(RedisKeyEnums.PACKET_SESSION_MENUS.getValue() + user.getUserId(), 0);
            redisUtils.rightPushAll(RedisKeyEnums.PACKET_SESSION_MENUS.getValue() + user.getUserId(), menus);
            redisUtils.expire(RedisKeyEnums.PACKET_SESSION_MENUS.getValue() + user.getUserId(), sessionTimeout);
        }
    }



    /**
     * 将用户session信息存入缓存
     *
     * @param user

     */
    public void resetSessionInfo(String sessionId, SessionDto user, long sessionTimeout) {
        if (user != null) {
            redisUtils.set(RedisKeyEnums.PACKET_SESSION_USER.getValue() + sessionId, JSON.toJSONString(user), sessionTimeout);
            redisUtils.expire(RedisKeyEnums.PACKET_SESSION_RIGHTS.getValue() + user.getUserId(), sessionTimeout);
            redisUtils.expire(RedisKeyEnums.PACKET_SESSION_APIS.getValue() + user.getUserId(), sessionTimeout);
            redisUtils.expire(RedisKeyEnums.PACKET_SESSION_ORGANIZATION_APIS.getValue() + user.getUserId(), sessionTimeout);
            redisUtils.expire(RedisKeyEnums.PACKET_SESSION_MENUS.getValue() + user.getUserId(), sessionTimeout);
        }


    }

    /**
     * 获取登录信息
     *
     * @param sessionId
     * @return
     */
    public SessionDto getSessionDto(String sessionId) {
        return redisUtils.get(RedisKeyEnums.PACKET_SESSION_USER.getValue() + sessionId) == null ? null : JSON.parseObject((String) redisUtils.get(RedisKeyEnums.PACKET_SESSION_USER.getValue() + sessionId), SessionDto.class);
    }

    /**
     * 获取用户权限值集合
     *
     * @param userId
     * @return
     */
    public List<String> getUserRightsList(String userId) {
        return (List<String>) (List) redisUtils.range(RedisKeyEnums.PACKET_SESSION_RIGHTS.getValue() + userId, 0, -1);
    }

    /**
     * 获取用户API接口路径集合
     *
     * @param userId
     * @return
     */
    public List<String> getUserApisList(String userId) {
        return (List<String>) (List) redisUtils.range(RedisKeyEnums.PACKET_SESSION_APIS.getValue() + userId, 0, -1);
    }

    /**
     * 获取用户组织架构API接口路径集合
     *
     * @param userId
     * @return
     */
    public List<String> getUserOriApisList(String userId) {
        return (List<String>) (List) redisUtils.range(RedisKeyEnums.PACKET_SESSION_ORGANIZATION_APIS.getValue() + userId, 0, -1);
    }


    /**
     * 获取用户菜单集合
     *
     * @param userId
     * @return
     */
    public List<ResourceDto> getUserResource(String userId) {
        return (List<ResourceDto>) (List) redisUtils.range(RedisKeyEnums.PACKET_SESSION_MENUS.getValue() + userId, 0, -1);
    }

    /**
     * 删除session
     *
     * @param sessionId
     */
    public void removeSession(String sessionId) {
        SessionDto sessionUser = getSessionDto(sessionId);
        if (sessionUser != null) {
            redisUtils.expire(RedisKeyEnums.PACKET_SESSION_RIGHTS.getValue() + sessionUser.getUserId(), 0);
            redisUtils.expire(RedisKeyEnums.PACKET_SESSION_MENUS.getValue() + sessionUser.getUserId(), 0);
            redisUtils.expire(RedisKeyEnums.PACKET_SESSION_USER.getValue() + sessionId, 0);
            redisUtils.expire(RedisKeyEnums.PACKET_SESSION_APIS.getValue() + sessionId, 0);
            redisUtils.expire(RedisKeyEnums.PACKET_SESSION_ORGANIZATION_APIS.getValue() + sessionId, 0);
        }
    }

}
