package com.quantum.infrastructure.wl.packet.bff.biz.facade.user;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import com.quantum.infrastructure.common.application.sdk.dto.ResourceDto;
import com.quantum.infrastructure.common.auth.sdk.AuthResultCode;
import com.quantum.infrastructure.common.auth.sdk.dto.LoginDto;
import com.quantum.infrastructure.common.auth.sdk.dto.RoleInfoDto;
import com.quantum.infrastructure.common.auth.sdk.dto.SessionUserInfoDto;
import com.quantum.infrastructure.common.auth.sdk.enums.AppCodeEnums;
import com.quantum.infrastructure.common.auth.sdk.interceptor.SessionInterceptor;
import com.quantum.infrastructure.common.basedata.sdk.enums.BaseStatusEnums;
import com.quantum.infrastructure.common.basedata.sdk.enums.DataChannleEnums;
import com.quantum.infrastructure.common.basedata.sdk.enums.IsDeleteStatusEnums;
import com.quantum.infrastructure.common.lessee.sdk.dto.LesseeDto;
import com.quantum.infrastructure.common.lessee.sdk.dto.LesseeGroupDto;
import com.quantum.infrastructure.common.lessee.sdk.enums.LesseeCacheKeyEnums;
import com.quantum.infrastructure.common.user.sdk.constant.CacheConstants;
import com.quantum.infrastructure.common.user.sdk.dto.*;
import com.quantum.infrastructure.common.user.sdk.enums.RoleNameEnums;
import com.quantum.infrastructure.common.util.exception.BusinessException;
import com.quantum.infrastructure.common.util.protocol.ChannelType;
import com.quantum.infrastructure.common.util.protocol.CommonResultCode;
import com.quantum.infrastructure.common.util.protocol.Result;
import com.quantum.infrastructure.common.util.protocol.dto.SessionDto;
import com.quantum.infrastructure.common.util.utils.CollectionHelpUtils;
import com.quantum.infrastructure.common.util.utils.MD5Util;
import com.quantum.infrastructure.common.util.utils.RedisUtils;
import com.quantum.infrastructure.common.util.utils.StringUtils;
import com.quantum.infrastructure.wl.packet.bff.biz.cache.UserDao;
import com.quantum.infrastructure.wl.packet.bff.biz.service.user.AuthService;
import com.quantum.infrastructure.wl.packet.bff.remote.user.*;
import com.quantum.infrastructure.wl.packet.bff.sdk.dto.BffLoginDto;
import com.quantum.infrastructure.wl.packet.bff.sdk.facade.user.AuthFacade;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Classname AuthFacadeImpl
 * @Date 2021/12/8 6:14 下午
 * @Created by miracle
 */
@Slf4j
@RestController
@RequestMapping("/auth")
public class AuthFacadeImpl implements AuthFacade {

    private static final String LOGIN_ROLE_ID = "roleId";
    private static final String LOGIN_ROLE_NAME = "roleName";

    public static final String LAST_LOGIN_ROLE_NAME_KEY = "login_role_name:%s:%s:%s";

    @Autowired
    private UserRemoteService userRemoteService;

    @Autowired
    private UserLesseeRemoteService userLesseeRemoteService;

    @Resource(name = "redisTemplate")
    private ValueOperations<String, SessionDto> valueOperations;

    @Autowired
    private UserDao userDao;

    @Autowired
    private HttpServletRequest request;

    @Autowired
    private AuthService authService;

    @Autowired
    private ResourceRemoteService resourceRemoteService;

    @Autowired
    private LesseeRemoteService lesseeRemoteService;

    @Autowired
    private LesseeGroupRemoteService lesseeGroupRemoteService;

    @Autowired
    private UserOrganizationRemoteService userOrganizationRemoteService;

    @Autowired
    private UserRoleRemoteService userRoleRemoteService;

    @Autowired
    private RoleRemoteService roleRemoteService;

    @Resource
    private RedisUtils redisUtils;

    @GlobalTransactional
    @Override
    public Result<Object> login(LoginDto loginDto) {
        Result<Object> result = new Result<>();
        Result<UserDto> userDtoResult = null;

        String appCode = StringUtils.isEmpty(loginDto.getAppCode()) ? AppCodeEnums.PACKET.getValue() : loginDto.getAppCode();


        //跨工程无token调用
        String key = LesseeCacheKeyEnums.INTERFACE_AUTH.getValue() + DataChannleEnums.ZBT.getValue() + "_" + appCode;
        authService.authorization(request, key, appCode, DataChannleEnums.ZBT.getValue());


        UserDto userDto = new UserDto();
        userDto.setLoginId(loginDto.getLoginId());
        userDto.setLoginPwd(MD5Util.getMD5String(loginDto.getLoginPwd()).toLowerCase());
        userDto.setIsDelete(IsDeleteStatusEnums.NORMAL.getValue());
        userDtoResult = userRemoteService.getOne(userDto, new String[]{"loginId", "loginPwd", "isDelete"});

        //兼容明文密码登录
        if (userDtoResult.getData() == null) {
            userDto.setLoginPwd(loginDto.getLoginPwd());
            userDtoResult = userRemoteService.getOne(userDto, new String[]{"loginId", "loginPwd", "isDelete"});
        }

        //兼容二次加密密码登录(OA跳转)
        if(userDtoResult.getData() == null){
            userDtoResult = userRemoteService.getOne(userDto, new String[]{"loginId", "isDelete"});
            if(userDtoResult != null) {
                if(userDtoResult.getData() != null && MD5Util.getMD5String(userDtoResult.getData().getLoginPwd()).equals(loginDto.getLoginPwd())){
                    log.info("OA跳转二次加密密码登录成功");
                } else {
                    userDtoResult.setData(null);
                }
            }
        }

        String lesseeId = "";
        String lesseeCode = "";
        String lesseeName;
        String lesseeNo;
        String innerFlag;
        String lesseeLogoUrl;
        if (userDtoResult.status()) {
            if (userDtoResult.getData() == null) {
                result.setResultCode(AuthResultCode.USER_NOT_EXISTS);
            } else {/**/
                // 存在用户信息，则生成token
                userDto = userDtoResult.getData();


                if(StringUtils.isEmpty(loginDto.getLoginLesseeId())) {
                    //判断是否有应用权限
                    List<Map> userLesseeList = userLesseeRemoteService.getLesseeUserApp(userDto.getId(), appCode).getData();
                    if (CollectionUtils.isEmpty(userLesseeList)) {
                        throw new BusinessException(CommonResultCode.BUSINESS_ERR, "您没有该应用权限，请联系管理员");
                    }

                    //租户去重
                    Set<String> lesseeIdSet = userLesseeList.stream().map(t-> t.get("lesseeId").toString()).collect(Collectors.toSet());
                    if (CollectionUtils.isEmpty(lesseeIdSet)) {
                        throw new BusinessException(CommonResultCode.BUSINESS_ERR, "您没有该应用权限，请联系管理员");
                    }
                    List<LesseeDto> lesseeDtoList = lesseeRemoteService.queryListByIds(CollectionHelpUtils.setToArray(lesseeIdSet, String.class), null).getData();
                    if (CollectionUtils.isEmpty(lesseeDtoList)) {
                        throw new BusinessException(CommonResultCode.BUSINESS_ERR, "您没有该应用权限，请联系管理员");
                    }
                    if(lesseeDtoList.size() == 1) {
                        lesseeId = lesseeDtoList.get(0).getId();
                        lesseeCode = lesseeDtoList.get(0).getLesseeCode();
                        lesseeName = lesseeDtoList.get(0).getLesseeName();
                        lesseeNo = lesseeDtoList.get(0).getLesseeNo();
                        lesseeLogoUrl = lesseeDtoList.get(0).getLogoUrl();
                        innerFlag = lesseeDtoList.get(0).getInnerFlag();
                    } else {
                        result.setMessage(CommonResultCode.MULTIPLE_LESSEE.getMessage());
                        result.setCode(CommonResultCode.MULTIPLE_LESSEE.getCode());
                        result.setData(lesseeDtoList);
                        return result;
                    }
                } else {
                    LesseeDto lesseeDto = new LesseeDto();
                    lesseeDto.setId(loginDto.getLoginLesseeId());
                    lesseeDto = lesseeRemoteService.getOne(lesseeDto, new String[]{"id"}).getData();

                    if (lesseeDto == null) {
                        throw new BusinessException(CommonResultCode.BUSINESS_ERR, "未开通账户信息，请联系管理员");
                    }

                    //再次验证租户用户关系表
                    UserLesseeDto userLesseeDto = new UserLesseeDto();
                    userLesseeDto.setUserId(userDto.getId());
                    userLesseeDto.setLesseeId(loginDto.getLoginLesseeId());
                    userLesseeDto = userLesseeRemoteService.getOne(userLesseeDto, new String[]{"userId", "lesseeId"}).getData();

                    if (userLesseeDto == null) {
                        throw new BusinessException(CommonResultCode.BUSINESS_ERR, "未开通账户信息，请联系管理员");
                    }

                    lesseeId = lesseeDto.getId();
                    lesseeCode = lesseeDto.getLesseeCode();
                    lesseeName = lesseeDto.getLesseeName();
                    lesseeNo = lesseeDto.getLesseeNo();
                    lesseeLogoUrl = lesseeDto.getLogoUrl();
                    innerFlag = lesseeDto.getInnerFlag();
                }

                LesseeGroupDto lesseeGroupDto = new LesseeGroupDto();
                lesseeGroupDto.setLesseeId(lesseeId);
                lesseeGroupDto.setAppCode(appCode);
                lesseeGroupDto.setIsDelete(IsDeleteStatusEnums.NORMAL.getValue());
                lesseeGroupDto = lesseeGroupRemoteService.getOne(lesseeGroupDto, new String[]{"lesseeId", "appCode", "isDelete"}).getData();

                LesseeDto resultLesseeDto = new LesseeDto();
                resultLesseeDto.setId(lesseeId);
                resultLesseeDto.setLesseeName(lesseeName);
                resultLesseeDto.setLesseeCode(lesseeCode);
                resultLesseeDto.setInnerFlag(innerFlag);

                List<ResourceDto> resourceDtos = resourceRemoteService.getUserResourceList(userDto.getId(), lesseeId, appCode, null).getData();//获取菜单
                List<RightsDto> rightsDtoList = userRemoteService.getRightsByUserId(userDto.getId(), lesseeId, appCode).getData();//获取权限
                List<RightsApiDto> rightsApiDtoList = userRemoteService.getRightsApiByUserId(userDto.getId(), lesseeId, appCode).getData();//获取权限
                List<OrganizationApiDto> organizationApiDtoList = userOrganizationRemoteService.getOriApiListByUserId(userDto.getId(), lesseeId, appCode).getData();//获取组织架构api

                List<String> rightsValues = CollectionUtils.isEmpty(rightsDtoList) ? null : rightsDtoList.stream().map(s -> s.getRightsCode()).collect(Collectors.toList());
                List<String> apisValues = CollectionUtils.isEmpty(rightsApiDtoList) ? null : rightsApiDtoList.stream().map(s -> s.getApiPath()).collect(Collectors.toList());
                List<String> organizationApi = CollectionUtils.isEmpty(organizationApiDtoList) ? null : organizationApiDtoList.stream().map(s -> s.getApiPath()).collect(Collectors.toList());

                //查询角色
                UserRoleMappingDto condition = new UserRoleMappingDto();
                condition.setUserId(userDto.getId());
                condition.setIsDelete(IsDeleteStatusEnums.NORMAL.getValue());
                condition.setLesseeId(lesseeId);
                List<UserRoleMappingDto> mappers = userRoleRemoteService.queryList(condition, null).getData();


                if (CollectionUtils.isEmpty(mappers)) {
                    throw new BusinessException(CommonResultCode.PARAM_ERR, "角色不可用, 请联系系统管理员");
                }


                if (resourceDtos == null || resourceDtos.size() == 0) {
                    throw new BusinessException(CommonResultCode.PARAM_ERR, "应用未授权或角色不可用, 请联系系统管理员");
                }


                if (BaseStatusEnums.DISABLE.getValue().equals(userDto.getStatus())) {
                    // 冻结用户，不允许登录
                    result.setResultCode(AuthResultCode.LOGIN_NOT_ALLOW);
                } else {
                    List<String> roleIds = mappers.stream().map(UserRoleMappingDto::getRoleId).collect(Collectors.toList());
                    List<RoleDto> roleDtoList = new ArrayList<>();
                    if (CollUtil.isNotEmpty(roleIds)) {
                        roleDtoList = roleRemoteService.queryListByIds(CollectionHelpUtils.listToArray(roleIds, String.class), null).getData();
                    }

                    String token = StringUtils.uuid32();
                    SessionDto sessionDto = new SessionDto();
                    sessionDto.setLesseeId(lesseeId);
                    sessionDto.setLesseeCode(lesseeCode);
                    sessionDto.setLoginId(userDto.getLoginId());
                    sessionDto.setUserId(userDto.getId());
                    sessionDto.setAlias(userDto.getAlias());
                    sessionDto.setUserIdCode(userDto.getIdCode());
                    sessionDto.setAppCode(appCode);
                    sessionDto.setLesseeNo(lesseeNo);
                    if (CollUtil.isNotEmpty(roleDtoList)){
                        RoleDto selectedRole;
                        if (roleDtoList.size() > 1 && RoleNameEnums.XIAOYAOPAI.getValue().equals(roleDtoList.get(0).getRoleName())) {
                            // 如果第一个角色是 逍遥派，则随机选择其他角色
                            List<RoleDto> filterRoles = roleDtoList.subList(1, roleDtoList.size());
                            selectedRole = filterRoles.get(new Random().nextInt(filterRoles.size()));
                        } else {
                            // 否则选择第一个角色
                            selectedRole = roleDtoList.get(0);
                        }
                        sessionDto.setCurrentRoleId(selectedRole.getId());
                        sessionDto.setCurrentRoleName(selectedRole.getRoleName());

                        final String loginRoleNameKey = String.format(LAST_LOGIN_ROLE_NAME_KEY, sessionDto.getAppCode(), sessionDto.getUserId(), sessionDto.getLesseeId());
                        Map<Object, Object> cacheRoleInfoValue = redisUtils.getHashEntries(loginRoleNameKey);
                        if (MapUtil.isNotEmpty(cacheRoleInfoValue)) {
                            boolean exists = roleDtoList.stream()
                                    .anyMatch(role -> role.getRoleName().equals(cacheRoleInfoValue.get(LOGIN_ROLE_NAME)));
                            if (exists) {
                                sessionDto.setCurrentRoleId((String) cacheRoleInfoValue.get(LOGIN_ROLE_ID));
                                sessionDto.setCurrentRoleName((String) cacheRoleInfoValue.get(LOGIN_ROLE_NAME));
                            }
                        } else {
                            Map<String, String> cacheRoleInfoMap = new HashMap<>();
                            cacheRoleInfoMap.put(LOGIN_ROLE_ID, sessionDto.getCurrentRoleId());
                            cacheRoleInfoMap.put(LOGIN_ROLE_NAME, sessionDto.getCurrentRoleName());
                            redisUtils.add(loginRoleNameKey, cacheRoleInfoMap);
                            redisUtils.expire(loginRoleNameKey, CacheConstants.CACHE_EXPIRATION_24H);
                        }
                    }
                    sessionDto.setRoleIds(roleIds);
                    setCache(ChannelType.WEB, sessionDto, token);

                    //设置菜单权限缓存
                    long sessionTimeout = SessionInterceptor.getSessionTimeout(ChannelType.WEB) * 60;//转换成分钟
                    userDao.setSessionInfo(token, sessionDto, resourceDtos, rightsValues, apisValues, organizationApi, sessionTimeout);

                    BffLoginDto bffLoginDto = new BffLoginDto();
                    bffLoginDto.setUserId(userDto == null ? "" : userDto.getId());
                    bffLoginDto.setSessionId(token);
                    bffLoginDto.setRightValues(rightsValues);
                    bffLoginDto.setResourceDtos(resourceDtos);
                    bffLoginDto.setLessee(resultLesseeDto);
                    bffLoginDto.setUserName(userDto.getAlias());
                    bffLoginDto.setLesseeGroupCode(lesseeGroupDto == null ? "" : lesseeGroupDto.getGroupCode());
                    bffLoginDto.setLesseeLogoUrl(lesseeLogoUrl == null ? "" : lesseeLogoUrl);
                    bffLoginDto.setRoleIds(roleIds);
                    result.setData(bffLoginDto);
                }

            }
        }


        return result;
    }


    @Override
    public Result<Integer> userStatus(String id, String status) {
        Result<Integer> result = new Result<>();

        UserDto userDto = new UserDto();
        userDto.setId(id);
        userDto.setIsDelete(IsDeleteStatusEnums.NORMAL.getValue());
        UserDto dbUserDto = userRemoteService.getOne(userDto, new String[]{"id", "isDelete"}).getData();

        if (dbUserDto != null) {
            UserDto upUserDto = new UserDto();
            upUserDto.setId(id);
            upUserDto.setStatus(status);
            result = userRemoteService.modifySelectiveByPrimaryKey(upUserDto);
        }
        return result;


    }

    private void setCache(ChannelType channelType, SessionDto sessionDto, String token) {
        long sessionTimeout = SessionInterceptor.getSessionTimeout(channelType);

        sessionDto.setChannelType(channelType.getKey().toString());

        valueOperations.set(SessionInterceptor.getSessionKey(token), sessionDto, sessionTimeout, TimeUnit.MINUTES);
    }


    @Override
    public Result<List<LesseeDto>> queryMulLessee(String loginId, String appCode) {

        String key = LesseeCacheKeyEnums.INTERFACE_AUTH.getValue() + DataChannleEnums.ZBT.getValue() + "_" + appCode;
        authService.authorization(request, key, appCode, DataChannleEnums.ZBT.getValue());

        Result<List<LesseeDto>> result = new Result<>();
        UserDto userDto = new UserDto();
        userDto.setLoginId(loginId);
        userDto = userRemoteService.getOne(userDto, new String[]{"loginId"}).getData();
        if (userDto != null) {
            List<Map> userLesseeList = userLesseeRemoteService.getLesseeUserApp(userDto.getId(), appCode).getData();
            if (!CollectionUtils.isEmpty(userLesseeList)) {
                //租户去重
                Set<String> lesseeIdSet = userLesseeList.stream().map(t -> t.get("lesseeId").toString()).collect(Collectors.toSet());
                if (!CollectionUtils.isEmpty(lesseeIdSet)) {
                    List<LesseeDto> lesseeDtoList = lesseeRemoteService.queryListByIds(CollectionHelpUtils.setToArray(lesseeIdSet, String.class), null).getData();
                    if (!CollectionUtils.isEmpty(lesseeDtoList) && lesseeDtoList.size() > 1) {
                        result.setData(lesseeDtoList);
                    }
                }
            }
        }
        return result;
    }

    @Override
    public Result<SessionUserInfoDto> getSessionUserInfo(String token) {
        Result<SessionUserInfoDto> result = new Result<>();

        SessionDto sessionDto = valueOperations.get(SessionInterceptor.getSessionKey(token));
        Assert.notNull(sessionDto,"登录信息获取异常");
        Assert.notNull(sessionDto.getUserId(),"登录信息获取异常");

        SessionUserInfoDto sessionUserInfoDto = new SessionUserInfoDto();
        sessionUserInfoDto.setAlias(sessionDto.getAlias());
        sessionUserInfoDto.setLoginId(sessionDto.getOpenId());
        sessionUserInfoDto.setUserId(sessionDto.getUserId());
        sessionUserInfoDto.setCurrentRoleId(sessionDto.getCurrentRoleId());
        sessionUserInfoDto.setCurrentRoleName(sessionDto.getCurrentRoleName());

        Result<List<RightsDto>> listResult = userRemoteService.getRightsByUserId(sessionDto.getUserId(),sessionDto.getLesseeId(),sessionDto.getAppCode());
        if (listResult.status() && listResult.getData() != null) {
            List<String> rightsValues = new ArrayList<>();
            List<RoleInfoDto> roles = new ArrayList<>();
            Map<String, String> roleNameMap = new HashMap<>();
            Map<String, List<String>> roleRightsMap = new HashMap<>();
            listResult.getData().forEach(t-> {
                rightsValues.add(t.getRightsCode());
                roleNameMap.put(t.getRoleId(), t.getRoleName());
                if(roleRightsMap.get(t.getRoleId()) == null) {
                    List<String> rights = new ArrayList<>();
                    rights.add(t.getRightsCode());
                    roleRightsMap.put(t.getRoleId(), rights);
                } else {
                    roleRightsMap.get(t.getRoleId()).add(t.getRightsCode());
                }
            });
            if(!CollectionUtils.isEmpty(roleRightsMap)) {
                roleRightsMap.forEach((k,v)-> {
                    RoleInfoDto roleInfoDto = new RoleInfoDto();
                    roleInfoDto.setRoleId(k);
                    roleInfoDto.setRoleRights(v);
                    roleInfoDto.setRoleName(roleNameMap.get(k));
                    roles.add(roleInfoDto);
                });
            }
            sessionUserInfoDto.setRoles(roles);
            sessionUserInfoDto.setRightsValues(rightsValues);
        }

        result.setData(sessionUserInfoDto);

        return result;
    }

    @Override
    public Result<String> switchCurrentRole(String roleId, String token) {
        Result<String> result = new Result<>();
        RoleDto roleDto = new RoleDto();
        roleDto.setId(roleId);
        roleDto = roleRemoteService.getOne(roleDto, new String[]{"id"}).getData();
        if (roleDto == null) {
            throw new BusinessException(CommonResultCode.BUSINESS_ERR, "角色不存在");
        }

        SessionDto sessionDto = valueOperations.get(SessionInterceptor.getSessionKey(token));
        if (sessionDto == null) {
            throw new BusinessException(CommonResultCode.BUSINESS_ERR, "登录信息不存在");
        }

        if (CollectionUtils.isEmpty(sessionDto.getRoleIds()) || !sessionDto.getRoleIds().contains(roleId)) {
            throw new BusinessException(CommonResultCode.BUSINESS_ERR, "当前用户不存在该角色");
        }

        sessionDto.setCurrentRoleName(roleDto.getRoleName());
        sessionDto.setCurrentRoleId(roleDto.getId());
        setCache(ChannelType.WEB, sessionDto, token);

        final String loginRoleNameKey = String.format(LAST_LOGIN_ROLE_NAME_KEY, sessionDto.getAppCode(), sessionDto.getUserId(), sessionDto.getLesseeId());
        Map<String, String> cacheRoleInfoMap = new HashMap<>();
        cacheRoleInfoMap.put(LOGIN_ROLE_ID, sessionDto.getCurrentRoleId());
        cacheRoleInfoMap.put(LOGIN_ROLE_NAME, sessionDto.getCurrentRoleName());
        redisUtils.add(loginRoleNameKey, cacheRoleInfoMap);
        redisUtils.expire(loginRoleNameKey, CacheConstants.CACHE_EXPIRATION_24H);

        result.setData(roleId);
        return result;
    }

}
