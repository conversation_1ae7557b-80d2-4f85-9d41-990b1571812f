package com.quantum.infrastructure.wl.packet.bff.biz.service.impl.user;

import com.quantum.infrastructure.common.application.sdk.dto.ext.ResourceExt;
import com.quantum.infrastructure.common.application.sdk.dto.ext.RightsExt;
import com.quantum.infrastructure.common.util.protocol.Result;
import com.quantum.infrastructure.wl.packet.bff.biz.service.user.ResourceService;
import com.quantum.infrastructure.wl.packet.bff.remote.user.ResourceRemoteService;
import com.quantum.infrastructure.wl.packet.bff.remote.user.RoleResourceMappingRemote;
import com.quantum.infrastructure.wl.packet.bff.remote.user.RoleRightsMappingRemote;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName ResourceServiceImpl.java
 * @Description
 * @createTime 2023年12月21日 15:45:00
 */
@Service
public class ResourceServiceImpl implements ResourceService {


    @Autowired
    private ResourceRemoteService resourceRemoteService;

    @Autowired
    private RoleResourceMappingRemote resourceMappingRemote;

    @Autowired
    private RoleRightsMappingRemote roleRightsMappingRemote;
    @Override
    public Result<List<ResourceExt>> queryResourceAndRightsList(String roleId, String lesseeId, String appCode) {
        Result<List<ResourceExt>> result = new Result<>();
        List<ResourceExt> resourceExtList = resourceRemoteService.getResourceAndRightTreeList().getData();

        //查询资源列表
        List<String> userResourceIds = resourceMappingRemote.queryResourceIdListByParm(roleId, lesseeId, appCode).getData();

        //查询权限列表
        List<String> rightIdList = roleRightsMappingRemote.queryIdsByRoleId(roleId).getData();


        if(!CollectionUtils.isEmpty(resourceExtList) && !CollectionUtils.isEmpty(rightIdList)){
            matchingRight(resourceExtList, rightIdList);
        }

        if(!CollectionUtils.isEmpty(resourceExtList) && !CollectionUtils.isEmpty(userResourceIds)){
            matchingResource(resourceExtList, userResourceIds);
        }

        result.setData(resourceExtList);
        return result;
    }

    private void matchingRight(List<ResourceExt> resourceExtList, List<String> rightIdList){
        if(!CollectionUtils.isEmpty(resourceExtList)){
            for(ResourceExt resourceExt : resourceExtList){
                if(!CollectionUtils.isEmpty(rightIdList)){
                    List<RightsExt> allRightList  = resourceExt.getRightList();
                    if(!CollectionUtils.isEmpty(allRightList)){
                        for(RightsExt rights : allRightList){
                            String userRight = rightIdList.stream().filter(s -> s.equals(rights.getId())).findAny().orElse(null);
                            rights.setChoose(userRight == null ? false : true);
                        }
                    }


                    //递归查询子资源菜单
                    List<ResourceExt> childsResourceList  = resourceExt.getChildsList();
                    if(CollectionUtils.isEmpty(childsResourceList)){
                        continue;
                    }else {
                        matchingRight(childsResourceList, rightIdList);
                    }

                }
            }
        }
    }

    private void matchingResource(List<ResourceExt> resourceExtList, List<String> userResourceIds){
        if(!CollectionUtils.isEmpty(resourceExtList)){
            for(ResourceExt resourceExt : resourceExtList){
                if(!CollectionUtils.isEmpty(userResourceIds)){
                    String resourceId = userResourceIds.stream().filter(s -> s.equals(resourceExt.getId())).findAny().orElse(null);
                    resourceExt.setChoose(resourceId == null ? false : true);

                    List<ResourceExt> allResourceList  = resourceExt.getChildsList();

                    if(CollectionUtils.isEmpty(allResourceList)){
                        continue;
                    }else {
                        matchingResource(allResourceList, userResourceIds);
                    }
                }
            }
        }
    }


}
