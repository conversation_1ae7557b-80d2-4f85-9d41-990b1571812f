package com.quantum.infrastructure.wl.packet.bff.biz.service.user;

import com.quantum.infrastructure.common.util.protocol.Result;

import javax.servlet.http.HttpServletRequest;

/**
 * @Classname AuthService
 * @Date 2021/12/14 7:51 下午
 * @Created by miracle
 */
public interface AuthService {


    /**
     * 查询意向客户列表
     *
     * @param request
     * @return
     */
    Result<Boolean> authorization(HttpServletRequest request, String key, String appCode, String channel);


}
