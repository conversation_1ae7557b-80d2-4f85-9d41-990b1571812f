package com.quantum.infrastructure.wl.packet.bff.biz.facade.order;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.quantum.infrastructure.common.user.sdk.dto.UserDto;
import com.quantum.infrastructure.common.util.protocol.BasePageRequest;
import com.quantum.infrastructure.common.util.protocol.Result;
import com.quantum.infrastructure.common.util.protocol.dto.PageDto;
import com.quantum.infrastructure.common.util.utils.CollectionHelpUtils;
import com.quantum.infrastructure.wl.packet.bff.remote.customer.IPacketCustomerRemote;
import com.quantum.infrastructure.wl.packet.bff.remote.order.IOrderDeclarationRemote;
import com.quantum.infrastructure.wl.packet.bff.remote.order.IOrderInfoRemote;
import com.quantum.infrastructure.wl.packet.bff.remote.supplier.INextSupplierRemote;
import com.quantum.infrastructure.wl.packet.bff.remote.user.UserRemoteService;
import com.quantum.infrastructure.wl.packet.bff.sdk.facade.order.OrderRestful;
import com.quantum.infrastructure.wl.packet.sdk.dto.CustomerDto;
import com.quantum.infrastructure.wl.packet.sdk.dto.NextSupplierDto;
import com.quantum.infrastructure.wl.packet.sdk.dto.OrderDeclarationDto;
import com.quantum.infrastructure.wl.packet.sdk.dto.OrderInfoDto;
import com.quantum.infrastructure.wl.packet.sdk.vo.request.OrderInfoReqVO;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/6/17 14:28
 */
@RestController
@RequestMapping("/order")
@RequiredArgsConstructor
public class OrderRestfulImpl implements OrderRestful {

    private final IOrderInfoRemote orderInfoRemote;
    private final UserRemoteService userRemoteService;
    private final IPacketCustomerRemote packetCustomerRemote;
    private final IOrderDeclarationRemote orderDeclarationRemote;
    private final INextSupplierRemote nextSupplierRemote;

    @Override
    public Result<String> createOrder(OrderInfoDto request) {
        return orderInfoRemote.createOrder(request);
    }

    @Override
    public Result<PageDto<OrderInfoDto>> listForPage(BasePageRequest<OrderInfoReqVO> request) {
        Result<PageDto<OrderInfoDto>> result = orderInfoRemote.listForPage(request);
        if (ObjectUtil.isNotNull(result.getData()) && CollUtil.isNotEmpty(result.getData().getList())) {
            Set<String> userIds = CollUtil.newHashSet();
            Set<String> customerIds = CollUtil.newHashSet();
            Set<String> orderIds = CollUtil.newHashSet();
            Set<String> supplierIds = CollUtil.newHashSet();

            for (OrderInfoDto orderInfoDto : result.getData().getList()) {
                userIds.add(orderInfoDto.getCreator());
                customerIds.add(orderInfoDto.getCustomerId());
                orderIds.add(orderInfoDto.getId());
                supplierIds.add(orderInfoDto.getNextPartyId());
            }

            CollUtil.removeNull(supplierIds);

            Map<String, UserDto> userDtoMap = Collections.emptyMap();
            if (CollUtil.isNotEmpty(userIds)) {
                userDtoMap = userRemoteService.queryListByIdsParseMap(CollectionHelpUtils.setToArray(userIds, String.class)).getData();
            }
            Map<String, CustomerDto> customerDtoMap = Collections.emptyMap();
            if (CollUtil.isNotEmpty(customerIds)) {
                customerDtoMap = packetCustomerRemote.queryListByIdsParseMap(CollectionHelpUtils.setToArray(customerIds, String.class)).getData();
            }
            Map<String, List<OrderDeclarationDto>> orderDeclarationMap = Collections.emptyMap();
            if (CollUtil.isNotEmpty(orderIds)) {
                orderDeclarationMap = orderDeclarationRemote.queryListByOrderIdsParseMap(CollectionHelpUtils.setToArray(orderIds, String.class)).getData();
            }
            Map<String, NextSupplierDto> supplierDeclarationMap = Collections.emptyMap();
            if (CollUtil.isNotEmpty(supplierIds)) {
                supplierDeclarationMap = nextSupplierRemote.queryListByIdsParseMap(CollectionHelpUtils.setToArray(supplierIds, String.class)).getData();
            }

            for (OrderInfoDto orderInfoDto : result.getData().getList()) {
                orderInfoDto.setCreator(userDtoMap.get(orderInfoDto.getCreator()) != null ? userDtoMap.get(orderInfoDto.getCreator()).getAlias() : null);
                CustomerDto customerDto = customerDtoMap.get(orderInfoDto.getCustomerId());
                Optional.ofNullable(customerDto).ifPresent(e -> {
                    orderInfoDto.setCustomerCode(e.getCustomerCode());
                    orderInfoDto.setCustomerName(e.getCustomerName());
                });
                NextSupplierDto nextSupplierDto = supplierDeclarationMap.get(orderInfoDto.getNextPartyId());
                Optional.ofNullable(nextSupplierDto).ifPresent(e -> {
                    orderInfoDto.setNextPartyCode(e.getSupplierCode());
                    orderInfoDto.setNextPartyName(e.getSupplierName());
                });
                orderInfoDto.setDeclarations(orderDeclarationMap.get(orderInfoDto.getId()));
            }
        }
        return result;
    }

    @Override
    public Result<Integer> updateOrderStatus(OrderInfoDto request) {
        return orderInfoRemote.updateOrderStatus(request);
    }

    @Override
    public Result<Integer> singleReceive(OrderInfoDto request) {
        return orderInfoRemote.singleReceive(request);
    }

    @Override
    public Result<Integer> singleShipment(OrderInfoDto request) {
        return orderInfoRemote.singleShipment(request);
    }
}
