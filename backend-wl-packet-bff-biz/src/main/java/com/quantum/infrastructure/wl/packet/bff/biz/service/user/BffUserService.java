package com.quantum.infrastructure.wl.packet.bff.biz.service.user;

import com.quantum.infrastructure.common.user.sdk.dto.RoleDto;
import com.quantum.infrastructure.common.util.protocol.Result;

import java.util.List;

/**
 * @Classname BffUserService
 * @Date 2022/2/15 6:05 下午
 * @Created by miracle
 */
public interface BffUserService {

    /**
     *
     * @param userId
     * @return
     */
     Result<List<RoleDto>> getRoleIdsByUserId(String userId);
}
