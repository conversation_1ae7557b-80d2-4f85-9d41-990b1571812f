package com.quantum.infrastructure.wl.packet.bff.biz.service.impl.product;


import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.quantum.infrastructure.common.user.sdk.dto.UserDto;
import com.quantum.infrastructure.common.util.exception.BusinessException;
import com.quantum.infrastructure.common.util.protocol.BasePageRequest;
import com.quantum.infrastructure.common.util.protocol.CommonResultCode;
import com.quantum.infrastructure.common.util.protocol.IsDeleteStatusEnums;
import com.quantum.infrastructure.common.util.protocol.Result;
import com.quantum.infrastructure.common.util.protocol.dto.PageDto;
import com.quantum.infrastructure.wl.packet.bff.biz.service.product.PacketProductService;
import com.quantum.infrastructure.wl.packet.bff.remote.product.IPacketProductRemote;
import com.quantum.infrastructure.wl.packet.bff.remote.user.UserRemoteService;
import com.quantum.infrastructure.wl.packet.sdk.dto.ProductDto;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @Author: liuquanli
 * @CreateTime: 2025-06-13
 */
@Service
@RequiredArgsConstructor
public class PacketProductServiceImpl implements PacketProductService {

    private final IPacketProductRemote productRemote;
    private final UserRemoteService userRemote;

    @GlobalTransactional
    @Override
    public Result<Integer> addProduct(ProductDto productDto) {
        checkProductDto(productDto);
        productRemote.create(productDto);
        return Result.ok();
    }

    @GlobalTransactional
    @Override
    public Result<Integer> updateProduct(ProductDto productDto) {
        checkProductDto(productDto);
        productRemote.modifySelective(productDto, updateColumns(), new String[]{"id"});
        return Result.ok();
    }

    @Override
    public Result<PageDto<ProductDto>> listProductForPage(BasePageRequest<ProductDto> basePageRequest) {
        Result<PageDto<ProductDto>> pageDtoResult = productRemote.listProductForPage(basePageRequest);
        //转译方法
        covertData(pageDtoResult.getData().getList());
        return pageDtoResult;
    }

    private void covertData(List<ProductDto> list) {
        if (!CollectionUtils.isEmpty(list)) {
            Set<String> userIdSet = new HashSet<>();
            for (ProductDto productDto : list) {
                userIdSet.add(productDto.getCreator());
            }
            Map<String, UserDto> userDtoMap = userRemote.getByIdsParseMap(userIdSet.toArray(new String[0])).getData();
            for (ProductDto productDto : list) {
                if (MapUtil.isNotEmpty(userDtoMap)) {
                    UserDto userDto = userDtoMap.get(productDto.getCreator());
                    productDto.setCreatorName(userDto.getAlias());
                }
            }
        }
    }

    private void checkProductDto(ProductDto productDto) {
        checkProductCode(productDto);
    }

    private String[] updateColumns() {
        return new String[]{"productCode", "productName", "productType", "expressType", "status"};
    }


    private void checkProductCode(ProductDto param) {
        ProductDto productByCode = getProductByCode(param.getProductCode());
        if (productByCode != null && !StrUtil.equals(param.getId(), productByCode.getId())) {
            throw new BusinessException(CommonResultCode.BUSINESS_ERR, "产品编码已存在");
        }
    }

    private ProductDto getProductByCode(String productCode) {
        ProductDto db = new ProductDto();
        db.setProductCode(productCode);
        db.setIsDelete(IsDeleteStatusEnums.NORMAL.getValue());
        return productRemote.getOne(db, new String[]{"productCode", "isDelete"}).getData();
    }

}
