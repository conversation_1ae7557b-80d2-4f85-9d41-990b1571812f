package com.quantum.infrastructure.wl.packet.bff.biz.facade.nextSupplier;

import com.quantum.infrastructure.common.util.exception.BusinessException;
import com.quantum.infrastructure.common.util.protocol.BasePageRequest;
import com.quantum.infrastructure.common.util.protocol.CommonResultCode;
import com.quantum.infrastructure.common.util.protocol.IsDeleteStatusEnums;
import com.quantum.infrastructure.common.util.protocol.Result;
import com.quantum.infrastructure.common.util.protocol.dto.PageDto;
import com.quantum.infrastructure.common.util.utils.StringUtils;
import com.quantum.infrastructure.wl.packet.bff.remote.supplier.INextSupplierRemote;
import com.quantum.infrastructure.wl.packet.bff.sdk.facade.nexSupplier.NextSupplierRestful;
import com.quantum.infrastructure.wl.packet.sdk.domain.vo.request.NextSupplierQueryReqVO;
import com.quantum.infrastructure.wl.packet.sdk.dto.NextSupplierDto;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/nextSupplier")
public class NextSupplierRestfulImpl implements NextSupplierRestful {

    @Resource
    private INextSupplierRemote nextSupplierRemoteService;

    @Override
    public Result<PageDto<NextSupplierDto>> queryNextSupplierForPage(@RequestBody BasePageRequest<NextSupplierQueryReqVO> request) {
        return nextSupplierRemoteService.queryNextSupplierForPage(request);
    }

    @GlobalTransactional
    @Override
    public Result<String> addNextSupplier(NextSupplierDto nextSupplierDto) {
        checkSupplierCode(null, nextSupplierDto.getSupplierCode());
        return nextSupplierRemoteService.create(nextSupplierDto);
    }

    @GlobalTransactional
    @Override
    public Result<Integer> updateNextSupplier(NextSupplierDto nextSupplierDto) {
        checkSupplierCode(nextSupplierDto.getId(), nextSupplierDto.getSupplierCode());
        return nextSupplierRemoteService.modifySelectiveByPrimaryKey(nextSupplierDto);
    }

    private void checkSupplierCode(String id, String supplierCode) {
        NextSupplierDto nextSupplierDto = new NextSupplierDto();
        nextSupplierDto.setSupplierCode(supplierCode);
        List<NextSupplierDto> queryList = nextSupplierRemoteService.queryList(nextSupplierDto, null).getData();
        if (!CollectionUtils.isEmpty(queryList)) {
            if (StringUtils.isEmpty(id)) {
                throw new BusinessException(CommonResultCode.BUSINESS_ERR, "下家代码不能重复");
            } else {
                for (NextSupplierDto temp : queryList) {
                    if (!temp.getId().equals(id)) {
                        throw new BusinessException(CommonResultCode.BUSINESS_ERR, "下家代码不能重复");
                    }
                }
            }
        }
    }

    @GlobalTransactional
    @Override
    public Result<Integer> deleteNextSupplier(String id) {
        NextSupplierDto deleteDto = new NextSupplierDto();
        deleteDto.setId(id);
        deleteDto.setIsDelete(IsDeleteStatusEnums.DELETE.getValue());
        return nextSupplierRemoteService.modifySelective(deleteDto, new String[]{"isDelete"}, new String[]{"id"});
    }

    @Override
    @GlobalTransactional
    public Result<Integer> updateStatus(String id, String status) {
        NextSupplierDto updateDto = new NextSupplierDto();
        updateDto.setId(id);
        updateDto.setStatus(status);
        return nextSupplierRemoteService.modifySelective(updateDto, new String[]{"status"}, new String[]{"id"});
    }
}
