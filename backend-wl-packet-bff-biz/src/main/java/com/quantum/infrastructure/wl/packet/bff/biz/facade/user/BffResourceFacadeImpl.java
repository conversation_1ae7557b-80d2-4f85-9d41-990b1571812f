package com.quantum.infrastructure.wl.packet.bff.biz.facade.user;

import com.quantum.infrastructure.common.application.sdk.dto.ResourceDto;
import com.quantum.infrastructure.common.application.sdk.dto.ext.ResourceExt;
import com.quantum.infrastructure.common.basedata.sdk.enums.YNStatusEnums;
import com.quantum.infrastructure.common.user.sdk.dto.RoleResourceMappingDto;
import com.quantum.infrastructure.common.util.protocol.Result;
import com.quantum.infrastructure.common.util.utils.StringUtils;
import com.quantum.infrastructure.common.util.utils.WebUtils;
import com.quantum.infrastructure.wl.packet.bff.biz.service.user.ResourceService;
import com.quantum.infrastructure.wl.packet.bff.remote.user.ResourceRemoteService;
import com.quantum.infrastructure.wl.packet.bff.remote.user.RoleResourceMappingRemote;
import com.quantum.infrastructure.wl.packet.bff.sdk.facade.user.BffResourceFacade;
import io.seata.spring.annotation.GlobalTransactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName BffResourceFacadeImpl.java
 * @Description
 * @createTime 2023年08月22日 14:40:00
 */
@RestController
@RequestMapping("resource")
public class BffResourceFacadeImpl implements BffResourceFacade {


    @Autowired
    private RoleResourceMappingRemote roleResourceMappingRemote;

    @Autowired
    private ResourceRemoteService resourceRemoteService;

    @Autowired
    private ResourceService resourceService;

    @GlobalTransactional
    @Override
    public Result<Integer> updateResource(ResourceDto resourceDto) {


        Result<Integer> result = resourceRemoteService.modifySelectiveByPrimaryKey(resourceDto);

        if (result.status()) {
            ResourceDto dbResourceDto = new ResourceDto();
            if (!StringUtils.isEmpty(resourceDto.getParentId()) && !YNStatusEnums.NO_NUM.getValue().equals(resourceDto.getParentId())) {
                dbResourceDto.setId(resourceDto.getId());
                dbResourceDto = resourceRemoteService.getOne(dbResourceDto, new String[]{"id"}).getData();

                if (dbResourceDto != null) {
                    String treePath = dbResourceDto.getTreePath();
                    String[] treePathArray = null;
                    if (!StringUtils.isEmpty(treePath)) {
                        treePathArray = treePath.split(",");
                    }

                    if (treePathArray != null && treePathArray.length > 0) {
                        RoleResourceMappingDto resourceMappingDto = new RoleResourceMappingDto();
                        resourceMappingDto.setAppCode(WebUtils.getSessionInfo().getAppCode());
                        resourceMappingDto.setResourceId(dbResourceDto.getId());
                        List<RoleResourceMappingDto> resourceMappingDtoList = roleResourceMappingRemote.queryList(resourceMappingDto, null).getData();

                        if (!CollectionUtils.isEmpty(resourceMappingDtoList)) {
                            Map<String, List<RoleResourceMappingDto>> roleResourceMappingMap = resourceMappingDtoList.stream().collect(Collectors.groupingBy(RoleResourceMappingDto::getRoleId));
                            for (String roleId : roleResourceMappingMap.keySet()) {

                                List<RoleResourceMappingDto> roleResourceMappingDtos = roleResourceMappingMap.get(roleId);

                                if (!CollectionUtils.isEmpty(roleResourceMappingDtos)) {
                                    for (String resourceId : treePathArray) {
                                        //保存菜单层级改动、同步改变角色菜单配置，如没有父级资源则需要增加

                                        RoleResourceMappingDto roleResourceMappingDto = new RoleResourceMappingDto();
                                        roleResourceMappingDto.setRoleId(roleId);
                                        roleResourceMappingDto.setResourceId(resourceId);
                                        roleResourceMappingDto.setLesseeId(WebUtils.getSessionInfo().getLesseeId());

                                        List<RoleResourceMappingDto> treePathroleResources = roleResourceMappingRemote.queryList(roleResourceMappingDto, null).getData();

                                        if (CollectionUtils.isEmpty(treePathroleResources)) {
                                            RoleResourceMappingDto saveRoleResource = new RoleResourceMappingDto();
                                            saveRoleResource.setRoleId(roleId);
                                            saveRoleResource.setResourceId(resourceId);
                                            saveRoleResource.setAppCode(WebUtils.getSessionInfo().getAppCode());
                                            saveRoleResource.setLesseeId(roleResourceMappingDtos.get(0).getLesseeId());
                                            saveRoleResource.setLesseeCode(roleResourceMappingDtos.get(0).getLesseeCode());
                                            roleResourceMappingRemote.create(saveRoleResource);
                                        }

                                    }
                                }

                            }
                        }

                    }

                }

            }
        }

        return result;
    }


    @Override
    public Result<List<ResourceExt>> queryResourceAndRightsList(String roleId) {
        return resourceService.queryResourceAndRightsList(roleId, WebUtils.getSessionInfo().getLesseeId(), WebUtils.getSessionInfo().getAppCode());
    }
}
