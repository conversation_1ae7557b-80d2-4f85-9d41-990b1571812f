package com.quantum.infrastructure.wl.packet.bff.biz.facade.product;

import com.quantum.infrastructure.common.util.protocol.BasePageRequest;
import com.quantum.infrastructure.common.util.protocol.Result;
import com.quantum.infrastructure.common.util.protocol.dto.PageDto;
import com.quantum.infrastructure.wl.packet.bff.biz.service.customer.PacketCustomerService;
import com.quantum.infrastructure.wl.packet.bff.biz.service.product.PacketProductService;
import com.quantum.infrastructure.wl.packet.bff.remote.product.IPacketProductRemote;
import com.quantum.infrastructure.wl.packet.bff.sdk.facade.product.ProductRestful;
import com.quantum.infrastructure.wl.packet.sdk.dto.ProductDto;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: liuquanli
 * @CreateTime: 2025-06-13
 */
@RestController
@RequestMapping("/product")
@RequiredArgsConstructor
public class ProductRestfulImpl implements ProductRestful {
    private final PacketProductService packetProductService;

    @Override
    public Result<Integer> addProduct(ProductDto productDto) {
        return packetProductService.addProduct(productDto);
    }

    @Override
    public Result<Integer> updateProduct(ProductDto productDto) {
        return packetProductService.updateProduct(productDto);
    }

    @Override
    public Result<PageDto<ProductDto>> listProductForPage(BasePageRequest<ProductDto> basePageRequest) {
        return packetProductService.listProductForPage(basePageRequest);
    }
}
