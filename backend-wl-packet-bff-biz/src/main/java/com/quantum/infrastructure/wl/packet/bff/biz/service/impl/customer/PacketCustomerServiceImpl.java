package com.quantum.infrastructure.wl.packet.bff.biz.service.impl.customer;

import cn.hutool.core.util.StrUtil;
import com.quantum.infrastructure.common.basedata.sdk.enums.BaseStatusEnums;
import com.quantum.infrastructure.common.basedata.sdk.enums.UserTypeEnums;
import com.quantum.infrastructure.common.basedata.sdk.enums.YNStatusEnums;
import com.quantum.infrastructure.common.user.sdk.dto.RoleDto;
import com.quantum.infrastructure.common.user.sdk.dto.UserDto;
import com.quantum.infrastructure.common.user.sdk.dto.UserLesseeDto;
import com.quantum.infrastructure.common.user.sdk.dto.UserRoleMappingDto;
import com.quantum.infrastructure.common.util.exception.BusinessException;
import com.quantum.infrastructure.common.util.protocol.BasePageRequest;
import com.quantum.infrastructure.common.util.protocol.CommonResultCode;
import com.quantum.infrastructure.common.util.protocol.IsDeleteStatusEnums;
import com.quantum.infrastructure.common.util.protocol.Result;
import com.quantum.infrastructure.common.util.protocol.dto.PageDto;
import com.quantum.infrastructure.common.util.utils.MD5Util;
import com.quantum.infrastructure.common.util.utils.WebUtils;
import com.quantum.infrastructure.wl.packet.bff.biz.service.customer.PacketCustomerService;
import com.quantum.infrastructure.wl.packet.bff.remote.customer.IPacketCustomerRemote;
import com.quantum.infrastructure.wl.packet.bff.remote.user.RoleRemoteService;
import com.quantum.infrastructure.wl.packet.bff.remote.user.UserLesseeRemoteService;
import com.quantum.infrastructure.wl.packet.bff.remote.user.UserRemoteService;
import com.quantum.infrastructure.wl.packet.bff.remote.user.UserRoleRemoteService;
import com.quantum.infrastructure.wl.packet.sdk.dto.CustomerDto;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

/**
 * @Author: liuquanli
 * @CreateTime: 2025-06-13
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class PacketCustomerServiceImpl implements PacketCustomerService {

    private final IPacketCustomerRemote customerRemote;

    private final UserRemoteService userRemoteService;

    private final UserLesseeRemoteService userLesseeRemoteService;

    private final RoleRemoteService roleRemoteService;

    private final UserRoleRemoteService userRoleRemoteService;

    @GlobalTransactional
    @Override
    public Result<Integer> addCustomer(CustomerDto customerDto) {
        //校验客户信息
        checkCustomerInfo(customerDto, null);
        //创建user
        String userId = createUserInfo(customerDto);
        customerDto.setUserId(userId);
        customerRemote.create(customerDto);
        return Result.ok();
    }

    @GlobalTransactional
    @Override
    public Result<Integer> updateCustomer(CustomerDto customerDto) {
        CustomerDto customerById = getCustomerById(customerDto.getId());
        Assert.notNull(customerById, "客户不存在");
        checkCustomerInfo(customerDto, customerById);
        updateUserLoginId(customerById, customerDto.getCustomerCode());
        customerRemote.modifySelective(customerDto, updateColumnNames(), new String[]{"id"});
        return Result.ok();
    }

    @Override
    public Result<PageDto<CustomerDto>> listCustomerForPage(BasePageRequest<CustomerDto> basePageRequest) {
        Result<PageDto<CustomerDto>> result = customerRemote.listCustomerForPage(basePageRequest);
        return result;
    }

    private void checkCustomerInfo(CustomerDto customerDto, CustomerDto db) {
        //校验客户编码不重复
        checkCustomerCode(customerDto);
        //校验user_loginId不重复
        checkUserLoginId(customerDto, db);
    }

    private void checkCustomerCode(CustomerDto customerDto) {
        //校验客户编码不重复
        CustomerDto db = getCustomerByCode(customerDto.getCustomerCode());
        if (db != null && !StrUtil.equals(db.getId(), customerDto.getId())) {
            throw new BusinessException(CommonResultCode.BUSINESS_ERR, "客户编码已存在");
        }
    }

    private void checkUserLoginId(CustomerDto customerDto, CustomerDto db) {
        UserDto userDB = getUserByLoginId(customerDto.getCustomerCode());
        if (userDB != null && !StrUtil.equals(userDB.getId(), db.getUserId())) {
            log.error("客户编码已存在,loginId:{}", customerDto.getCustomerCode());
            throw new BusinessException(CommonResultCode.BUSINESS_ERR, "客户编码已存在,用户登录名");
        }
    }


    private CustomerDto getCustomerById(String Id) {
        //根据客户编码查询客户信息
        CustomerDto param = new CustomerDto();
        param.setId(Id);
        param.setIsDelete(IsDeleteStatusEnums.NORMAL.getValue());
        return customerRemote.getOne(param, new String[]{"id", "isDelete"}).getData();
    }

    private CustomerDto getCustomerByCode(String customerCode) {
        //根据客户编码查询客户信息
        CustomerDto param = new CustomerDto();
        param.setCustomerCode(customerCode);
        param.setIsDelete(IsDeleteStatusEnums.NORMAL.getValue());
        return customerRemote.getOne(param, new String[]{"customerCode", "isDelete"}).getData();
    }

    private UserDto getUserByLoginId(String customerCode) {
        //根据用户编码查询用户信息
        UserDto param = new UserDto();
        param.setLoginId(customerCode);
        param.setIsDelete(IsDeleteStatusEnums.NORMAL.getValue());
        return userRemoteService.getOne(param, new String[]{"loginId", "isDelete"}).getData();
    }

    private void updateUserLoginId(CustomerDto db, String customerCode) {
        UserDto updateUser = new UserDto();
        updateUser.setId(db.getUserId());
        updateUser.setLoginId(customerCode);
        userRemoteService.modifySelective(updateUser, new String[]{"loginId"}, new String[]{"id"});
    }

    /**
     * 更新的列名
     */
    private String[] updateColumnNames() {
        return new String[]{"customerCode", "customerName", "customerType", "phone", "status"};
    }

    private String createUserInfo(CustomerDto customer) {
        //用户创建
        UserDto user = new UserDto();
        user.setType(UserTypeEnums.CUSTOMER.getValue());
        user.setStatus(BaseStatusEnums.ENABLE.getValue());
        user.setLoginId(customer.getCustomerCode());
        user.setAlias(customer.getCustomerName());
        user.setIsSystem(YNStatusEnums.NO.getValue());
        user.setEnterpriseAuthStatus(YNStatusEnums.YES.getValue());
        user.setMobile(customer.getPhone());
        user.setLoginPwd("123456");
        user.setResetDefaultPassword(YNStatusEnums.NO_NUM.getValue());
        user.setExtension("创建packet客户同步");
        String uid = userRemoteService.createUser(user).getData();

        //用户租户关联
        UserLesseeDto userLessee = new UserLesseeDto();
        userLessee.setUserId(uid);
        userLessee.setLesseeId(WebUtils.getSessionLesseeId());
        userLessee.setDefaultFlag(YNStatusEnums.NO_NUM.getValue());
        userLesseeRemoteService.create(userLessee);

        //初始化关联角色
        initCustomerUserRole(uid);
        return uid;
    }

    private void initCustomerUserRole(String userId) {
        RoleDto roleDto = new RoleDto();
        roleDto.setRoleName("客户");
        roleDto.setAppCode("packet");
        roleDto = roleRemoteService.getOne(roleDto, new String[]{"roleName", "appCode"}).getData();
        if (roleDto == null) {
            throw new BusinessException(CommonResultCode.BUSINESS_ERR, "客户角色不存在,先创建");
        }

        UserRoleMappingDto userRoleMappingDto = new UserRoleMappingDto();
        userRoleMappingDto.setUserId(userId);
        userRoleMappingDto.setRoleId(roleDto.getId());
        userRoleRemoteService.create(userRoleMappingDto);
    }

}
