package com.quantum.infrastructure.wl.packet.bff.biz.service.customer;

import com.quantum.infrastructure.common.util.protocol.BasePageRequest;
import com.quantum.infrastructure.common.util.protocol.Result;
import com.quantum.infrastructure.common.util.protocol.dto.PageDto;
import com.quantum.infrastructure.wl.packet.sdk.dto.CustomerDto;

/**
 * @Author: liuquanli
 * @CreateTime: 2025-06-13
 */
public interface PacketCustomerService {

    Result<Integer> addCustomer(CustomerDto customerDto);

    Result<Integer> updateCustomer(CustomerDto customerDto);

    Result<PageDto<CustomerDto>> listCustomerForPage(BasePageRequest<CustomerDto> basePageRequest);
}
