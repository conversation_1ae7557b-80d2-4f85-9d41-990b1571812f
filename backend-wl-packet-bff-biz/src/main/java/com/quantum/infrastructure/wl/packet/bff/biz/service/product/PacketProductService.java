package com.quantum.infrastructure.wl.packet.bff.biz.service.product;

import com.quantum.infrastructure.common.util.protocol.BasePageRequest;
import com.quantum.infrastructure.common.util.protocol.Result;
import com.quantum.infrastructure.common.util.protocol.dto.PageDto;
import com.quantum.infrastructure.wl.packet.sdk.dto.ProductDto;

/**
 * @Author: liuquanli
 * @CreateTime: 2025-06-13
 */
public interface PacketProductService {
    Result<Integer> addProduct(ProductDto productDto);

    Result<Integer> updateProduct(ProductDto productDto);

    Result<PageDto<ProductDto>> listProductForPage(BasePageRequest<ProductDto> basePageRequest);
}
