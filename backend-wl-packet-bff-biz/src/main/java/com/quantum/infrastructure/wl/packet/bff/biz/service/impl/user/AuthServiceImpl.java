package com.quantum.infrastructure.wl.packet.bff.biz.service.impl.user;

import com.alibaba.fastjson.JSON;
import com.quantum.infrastructure.common.lessee.sdk.dto.LesseeInterfaceAuthDto;
import com.quantum.infrastructure.common.lessee.sdk.facade.LesseeInterfaceAuthFacade;
import com.quantum.infrastructure.common.util.exception.BusinessException;
import com.quantum.infrastructure.common.util.protocol.CommonResultCode;
import com.quantum.infrastructure.common.util.protocol.Result;
import com.quantum.infrastructure.common.util.utils.MD5Util;
import com.quantum.infrastructure.common.util.utils.RedisUtils;
import com.quantum.infrastructure.wl.packet.bff.biz.service.user.AuthService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;
import java.util.TreeMap;

/**
 * @Classname AuthServiceImpl
 * @Date 2021/12/14 7:52 下午
 * @Created by miracle
 */
@Service
public class AuthServiceImpl implements AuthService {

    @Autowired
    RedisUtils redisUtils;

    @Autowired
    private LesseeInterfaceAuthFacade lesseeInterfaceAuthFacade;

    @Override
    public Result<Boolean> authorization(HttpServletRequest request, String key, String appCode, String channel) {
        Result<Boolean> result = new Result<>();

        LesseeInterfaceAuthDto lesseeInterfaceAuthDto = redisUtils.get(key) == null ? null : JSON.parseObject((String) redisUtils.get(key), LesseeInterfaceAuthDto.class);

        if (lesseeInterfaceAuthDto == null) {
            Result<String> initCacheResult = lesseeInterfaceAuthFacade.initCache(appCode, channel);
            if (!initCacheResult.status()) {
                throw new BusinessException(CommonResultCode.PARAM_ERR, initCacheResult.getMessage());
            }

        }

        lesseeInterfaceAuthDto =  redisUtils.get(key) == null ? null : JSON.parseObject((String) redisUtils.get(key), LesseeInterfaceAuthDto.class);

        if (lesseeInterfaceAuthDto != null) {
            String timestamp = String.valueOf(System.currentTimeMillis());
            Map<String, String> paramMap = new TreeMap<>();
            paramMap.put("appKey", lesseeInterfaceAuthDto.getAppKey());
            paramMap.put("appSecret", lesseeInterfaceAuthDto.getAppSecret());
            paramMap.put("timestamp", timestamp);
            String sign = MD5Util.getSignToken(paramMap, true);

            RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
            if (requestAttributes == null) {
                requestAttributes = new ServletRequestAttributes(new MockHttpServletRequest());
            }
            RequestContextHolder.setRequestAttributes(requestAttributes);

            request.setAttribute("timestamp", timestamp);
            request.setAttribute("sign", sign);
            request.setAttribute("key", key);

        }

        return result;
    }

}
