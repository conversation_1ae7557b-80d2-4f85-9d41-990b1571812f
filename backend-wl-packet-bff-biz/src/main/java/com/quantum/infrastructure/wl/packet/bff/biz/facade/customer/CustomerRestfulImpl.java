package com.quantum.infrastructure.wl.packet.bff.biz.facade.customer;

import com.quantum.infrastructure.common.util.protocol.BasePageRequest;
import com.quantum.infrastructure.common.util.protocol.Result;
import com.quantum.infrastructure.common.util.protocol.dto.PageDto;
import com.quantum.infrastructure.wl.packet.bff.biz.service.customer.PacketCustomerService;
import com.quantum.infrastructure.wl.packet.bff.sdk.facade.customer.CustomerRestful;
import com.quantum.infrastructure.wl.packet.sdk.dto.CustomerDto;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: liuquanli
 * @CreateTime: 2025-06-13
 */
@RestController
@RequestMapping("/customer")
@RequiredArgsConstructor
public class CustomerRestfulImpl implements CustomerRestful {


    private final PacketCustomerService packetCustomerService;

    @Override
    public Result<Integer> addCustomer(CustomerDto customerDto) {
        return packetCustomerService.addCustomer(customerDto);
    }

    @Override
    public Result<Integer> updateCustomer(CustomerDto customerDto) {
        return packetCustomerService.updateCustomer(customerDto);
    }


    @Override
    public Result<PageDto<CustomerDto>> listCustomerForPage(BasePageRequest<CustomerDto> basePageRequest) {
        return packetCustomerService.listCustomerForPage(basePageRequest);
    }


}
