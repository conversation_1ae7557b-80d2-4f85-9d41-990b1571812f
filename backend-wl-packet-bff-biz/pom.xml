<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.quantum.infrastructure</groupId>
        <artifactId>backend-wl-packet-bff</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <packaging>jar</packaging>

    <artifactId>backend-wl-packet-bff-biz</artifactId>
    <name>backend-wl-packet-bff-biz</name>

    <dependencies>
        <dependency>
            <groupId>com.quantum.infrastructure</groupId>
            <artifactId>backend-wl-packet-bff-dao</artifactId>
        </dependency>
        <dependency>
            <groupId>com.quantum.infrastructure</groupId>
            <artifactId>backend-wl-packet-bff-remote</artifactId>
        </dependency>
        <dependency>
            <groupId>com.quantum.infrastructure</groupId>
            <artifactId>backend-wl-packet-bff-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.quantum.infrastructure</groupId>
            <artifactId>backend-common-framework-biz</artifactId>
        </dependency>
        <!--引入Feign,可以声明的方式调用微服务-->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <!--引入服务容错 Hystrix 的依赖-->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-netflix-hystrix</artifactId>
        </dependency>
        <dependency>
            <groupId>com.quantum.infrastructure</groupId>
            <artifactId>backend-common-seata-client-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
        </dependency>
    </dependencies>
</project>
