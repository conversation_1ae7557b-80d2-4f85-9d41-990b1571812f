<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.quantum.infrastructure</groupId>
        <artifactId>backend-wl-packet-bff</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <packaging>war</packaging>

    <artifactId>backend-wl-packet-bff-main</artifactId>
    <name>backend-wl-packet-bff-main</name>

    <dependencies>
        <dependency>
            <groupId>com.quantum.infrastructure</groupId>
            <artifactId>backend-wl-packet-bff-biz</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-core</artifactId>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
        </dependency>
        <!-- test -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.spockframework</groupId>
            <artifactId>spock-spring</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <!--<scope>test</scope>-->
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>guava</artifactId>
                    <groupId>com.google.guava</groupId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.name}</finalName>

        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.2.2.RELEASE</version>
                <configuration>
                    <mainClass>com.quantum.infrastructure.project.main.Application</mainClass>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>2.6</version>
                <configuration>
                    <failOnMissingWebXml>false</failOnMissingWebXml>
                </configuration>
            </plugin>
        </plugins>

        <!-- profile对资源的操作 -->
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <excludes>
                    <exclude>application*.yml</exclude>
                </excludes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <!-- 是否替换@xx@表示的maven properties属性值 -->
                <filtering>true</filtering>
                <includes>
                    <include>bootstrap.yml</include>
                    <include>application-${spring.profiles.active}.yml</include>
                </includes>
            </resource>
        </resources>
    </build>

    <profiles>
        <!--local默认激活，使用idea Spring Boot 配置启动工程，需要local的配置-->
        <profile>
            <id>local</id>
            <properties>
                <spring.profiles.active>local</spring.profiles.active>
                <spring.nacos.host>***************</spring.nacos.host>
                <spring.nacos.username>nacos</spring.nacos.username>
                <spring.nacos.password>nacos</spring.nacos.password>
                <spring.nacos.namespace>1511f407-9cb5-4aab-8a57-ba6c68947e62</spring.nacos.namespace>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <spring.profiles.active>test</spring.profiles.active>
                <spring.nacos.host>***************</spring.nacos.host>
                <spring.nacos.username>nacos</spring.nacos.username>
                <spring.nacos.password>nacos</spring.nacos.password>
                <spring.nacos.namespace>1511f407-9cb5-4aab-8a57-ba6c68947e62</spring.nacos.namespace>
            </properties>

        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <spring.profiles.active>prod</spring.profiles.active>
                <spring.nacos.host>**************</spring.nacos.host>
                <spring.nacos.username>nacos</spring.nacos.username>
                <spring.nacos.password>zbt1q2w3e4r</spring.nacos.password>
                <spring.nacos.namespace>1511f407-9cb5-4aab-8a57-ba6c68947e62</spring.nacos.namespace>
            </properties>
        </profile>
    </profiles>
</project>
