package com.quantum.infrastructure.wl.packet.bff.dao

import com.alibaba.fastjson.JSON
import com.quantum.infrastructure.wl.packet.bff.BaseSpec
import com.quantum.infrastructure.wl.packet.bff.dao.entity.User
import com.quantum.infrastructure.wl.packet.bff.dao.mapper.UserMapper
import org.springframework.beans.factory.annotation.Autowired

/**
 * <AUTHOR>
 * @Date 2019/8/30
 */
class UserDaoSpec extends BaseSpec {

    @Autowired
    private UserMapper userMapper;

    User user = null;

    def setup() {
        user = new User();
        user.setId(1);
        user.setLoginId("liuzengrong");
        user.setLoginPwd("123456");
        user.setSex("M");
        user.setMobile("18688858151");
        user.setEmail("<EMAIL>");
        user.setCreator("zengrong.lzr");
        user.setCreateTime(new Date());
        user.setUpdator("zengrong.lzr");
        user.setUpdateTime(new Date())
    }

    def "insert user test"() {
        int rows = 0;
        when:
            rows = userMapper.insert(user);
        then:
            rows > 0 ? true : false;
    }

    def "query users test"() {
        List<User> userList = null;
        when:
            userList = userMapper.selectAll(User.class);
            println(JSON.toJSONString(userList));
        then:
            userList != null && userList.size() > 0;
    }

}
