CREATE TABLE ac_user (
  id bigint not null auto_increment comment '流水主键',
  login_id varchar(60) not null comment '用户名',
  login_pwd VARCHAR(32) NOT NULL COMMENT '密码',
  mobile VARCHAR(15) NOT NULL COMMENT '手机',
  email varchar(100) not null comment '邮箱',
  sex varchar(1) not null comment '性别',
  creator varchar(20) not null comment '创建人',
  create_time datetime not null comment '创建时间',
  updator varchar(20) not null comment '修改人',
  update_time datetime not null comment '修改时间',
  extension varchar(1024) comment '扩展字段',
  PRIMARY KEY (id)
);