<?xml version="1.0" encoding="UTF-8"?>
<configuration>
	<property name="LOG_TEMP" value="./logs"/>
	<include resource="org/springframework/boot/logging/logback/base.xml" />

	<springProfile name="local">
		<logger name="com.quantum" level="DEBUG" />
		<logger name="org.springframework" level="DEBUG"/>
		<logger name="ch.qos.logback.core.rolling.TimeBasedRollingPolicy" level="DEBUG"/>
		<logger name="com.quantum.infrastructure" level="DEBUG">
			<appender-ref ref="CONSOLE" />
		</logger>
	</springProfile>

	<springProfile name="dev">
		<include resource="logback-common.xml" />
		<root level="INFO">
			<appender-ref ref="default-appender"/>
		</root>
	</springProfile>
	<springProfile name="test">
		<logger name="com.quantum" level="DEBUG" />
		<logger name="org.springframework" level="DEBUG"/>
		<logger name="ch.qos.logback.core.rolling.TimeBasedRollingPolicy" level="DEBUG"/>
		<logger name="com.quantum.infrastructure" level="DEBUG">
			<appender-ref ref="CONSOLE" />
		</logger>
	</springProfile>
	<springProfile name="pre">
		<logger name="io.seata" level="INFO"/>
		<include resource="logback-common.xml" />
		<root level="DEBUG">
			<appender-ref ref="ROOT"/>
			<appender-ref ref="default-appender"/>
		</root>
	</springProfile>
	<springProfile name="prod">
		<logger name="io.seata" level="INFO"/>
		<include resource="logback-common.xml" />
		<root level="DEBUG">
			<appender-ref ref="ROOT"/>
			<appender-ref ref="default-appender"/>
		</root>
	</springProfile>

</configuration>
