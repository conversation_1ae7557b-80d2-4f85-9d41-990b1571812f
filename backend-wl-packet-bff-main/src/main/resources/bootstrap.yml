spring:
  application:
    name: backend-wl-packet-bff
  profiles:
    active: @spring.profiles.active@
  cloud:
    nacos:
      config:
        enabled: true
        server-addr: ${nacos.url}
        file-extension: yml # 配置内容的数据格式，默认为 properties
        shared-configs[0]:
          data-id: quantum-sys-config.yml
        namespace: ${nacos.namespace}
        userName: @spring.nacos.username@
        password: @spring.nacos.password@
        group: SEATA_GROUP
      discovery:
        server-addr: ${nacos.url}
        namespace: ${nacos.namespace}
        userName: @spring.nacos.username@
        password: @spring.nacos.password@
        group: SEATA_GROUP
  main:
    allow-bean-definition-overriding: true
    aop:
      proxy-target-class: true

feign:
  hystrix:
    enabled: true
    client:
      config:
        default:
          connectTimeout: 15000
          readTimeout: 15000
  compression:
    request:
      min-request-size: 153600


#hystrix的超时时间
hystrix:
  command:
    default:
      execution:
        isolation:
          strategy: SEMAPHORE  #加上这个就可以获取到HttpServletRequest
          thread:
            timeoutInMilliseconds: 15000
ribbon:
  ReadTimeout: 10000
  ConnectTimeout: 3000
  MaxAutoRetries: 0 #同一台实例最大重试次数,不包括首次调用
  MaxAutoRetriesNextServer: 1 #重试负载均衡其他的实例最大重试次数,不包括首次调用
  OkToRetryOnAllOperations: false  #是否所有操作都重试
  MaxConnectionsPerHost: 3000
  MaxTotalConnections: 3000



logging:
  level:
    com.alibaba.nacos.client.*: WARN

server:
  port: 9992

##------------nacos-------------------------------------
nacos:
  host: @spring.nacos.host@
  port: 8848
  url: ${nacos.host}:${nacos.port}
  namespace : 1511f407-9cb5-4aab-8a57-ba6c68947e62

opentracing:
  jaeger:
    enabled: true




appKey: 638f8d4ee7904c2d8a5f1c89339fc4cf


