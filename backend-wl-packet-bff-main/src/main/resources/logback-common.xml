<?xml version="1.0" encoding="UTF-8"?>
<included>
	<property name="LOGS_ROOT" value="/home/<USER>/zbt/tomcat/logs/packet"/>
	<property name="DEFAULT_MESSAGE_FORMAT" value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] [%-5level] %logger{0} - %msg%n"/>

	<appender name="ROOT" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${LOGS_ROOT}/root.log</file>
		<filter class="ch.qos.logback.classic.filter.ThresholdFilter">
			<level>TRACE</level>
		</filter>
		<!-- 可让每天产生一个日志文件，最多 10 个，自动回滚 -->
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${LOGS_ROOT}/root.log.%d{yyyy-MM-dd}</fileNamePattern>
			<maxHistory>10</maxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger - %msg%n</pattern>
		</encoder>
	</appender>

	<appender name="errors-appender" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${LOGS_ROOT}/errors.log</file>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${LOGS_ROOT}/errors-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
			<maxHistory>20</maxHistory>
			<TimeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
				<MaxFileSize>200MB</MaxFileSize>
			</TimeBasedFileNamingAndTriggeringPolicy>
		</rollingPolicy>
		<encoder>
			<pattern>${DEFAULT_MESSAGE_FORMAT}</pattern>
			<charset>UTF-8</charset>
		</encoder>
		<filter class="ch.qos.logback.classic.filter.LevelFilter">
			<level>ERROR</level>
			<onMatch>ACCEPT</onMatch>
			<onMismatch>DENY</onMismatch>
		</filter>
	</appender>

	<appender name="warns-appender" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${LOGS_ROOT}/warns.log</file>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${LOGS_ROOT}/warns-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
			<maxHistory>20</maxHistory>
			<TimeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
				<MaxFileSize>200MB</MaxFileSize>
			</TimeBasedFileNamingAndTriggeringPolicy>
		</rollingPolicy>
		<encoder>
			<pattern>${DEFAULT_MESSAGE_FORMAT}</pattern>
			<charset>UTF-8</charset>
		</encoder>
		<filter class="ch.qos.logback.classic.filter.LevelFilter">
			<level>WARN</level>
			<onMatch>ACCEPT</onMatch>
			<onMismatch>DENY</onMismatch>
		</filter>
	</appender>

	<appender name="core-appender" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${LOGS_ROOT}/core.log</file>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${LOGS_ROOT}/core-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
			<maxHistory>20</maxHistory>
			<TimeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
				<MaxFileSize>200MB</MaxFileSize>
			</TimeBasedFileNamingAndTriggeringPolicy>
		</rollingPolicy>
		<encoder>
			<pattern>${DEFAULT_MESSAGE_FORMAT}</pattern>
			<charset>UTF-8</charset>
		</encoder>
	</appender>

	<appender name="default-appender" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${LOGS_ROOT}/default.log</file>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${LOGS_ROOT}/default-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
			<maxHistory>20</maxHistory>
			<TimeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
				<MaxFileSize>200MB</MaxFileSize>
			</TimeBasedFileNamingAndTriggeringPolicy>
		</rollingPolicy>
		<encoder>
			<pattern>${DEFAULT_MESSAGE_FORMAT}</pattern>
			<charset>UTF-8</charset>
		</encoder>
	</appender>

	<appender name="dal-digest-appender" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${LOGS_ROOT}/dal-digest.log</file>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${LOGS_ROOT}/dal-digest-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
			<maxHistory>10</maxHistory>
			<TimeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
				<MaxFileSize>200MB</MaxFileSize>
			</TimeBasedFileNamingAndTriggeringPolicy>
		</rollingPolicy>
		<encoder>
			<pattern>${DEFAULT_MESSAGE_FORMAT}</pattern>
			<charset>UTF-8</charset>
		</encoder>
	</appender>
	<appender name="sal-digest-appender" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${LOGS_ROOT}/sal-digest.log</file>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${LOGS_ROOT}/sal-digest-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
			<maxHistory>10</maxHistory>
			<TimeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
				<MaxFileSize>200MB</MaxFileSize>
			</TimeBasedFileNamingAndTriggeringPolicy>
		</rollingPolicy>
		<encoder>
			<pattern>${DEFAULT_MESSAGE_FORMAT}</pattern>
			<charset>UTF-8</charset>
		</encoder>
	</appender>
	<appender name="api-digest-appender" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${LOGS_ROOT}/api-digest.log</file>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${LOGS_ROOT}/api-digest-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
			<maxHistory>10</maxHistory>
			<TimeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
				<MaxFileSize>200MB</MaxFileSize>
			</TimeBasedFileNamingAndTriggeringPolicy>
		</rollingPolicy>
		<encoder>
			<pattern>${DEFAULT_MESSAGE_FORMAT}</pattern>
			<charset>UTF-8</charset>
		</encoder>
		<triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<maxFileSize>100MB</maxFileSize>
		</triggeringPolicy>
	</appender>
	<!-- 数据库框架操作日志 -->
	<appender name="sql-appender" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${LOGS_ROOT}/sql.log</file>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${LOGS_ROOT}/sql-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
			<maxHistory>10</maxHistory>
			<TimeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
				<MaxFileSize>200MB</MaxFileSize>
			</TimeBasedFileNamingAndTriggeringPolicy>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger - %msg%n</pattern>
			<charset>UTF-8</charset>
		</encoder>
	</appender>
	<appender name="druid-appender" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${LOGS_ROOT}/druid.log</file>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${LOGS_ROOT}/druid-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
			<maxHistory>20</maxHistory>
			<TimeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
				<MaxFileSize>200MB</MaxFileSize>
			</TimeBasedFileNamingAndTriggeringPolicy>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %msg%n</pattern>
			<charset>UTF-8</charset>
		</encoder>
	</appender>

	<root>
		<level value="${log.level}" />
		<appender-ref ref="ROOT" />
		<appender-ref ref="errors-appender" />
	</root>

	<logger name="org.apache.zookeeper" level="WARN"/>
	<logger name="redis.clients" level="WARN"/>
	<logger name="com.alibaba" level="WARN"/>

	<logger name="com.magic.x.skeletonweb" additivity="false">
		<appender-ref ref="errors-appender"/>
		<appender-ref ref="warns-appender"/>
		<appender-ref ref="default-appender"/>
	</logger>

	<!-- digest logger -->
	<logger name="dal-digest-log" additivity="false">
		<level value="info"/>
		<appender-ref ref="dal-digest-appender"/>
		<appender-ref ref="errors-appender"/>
	</logger>

	<!-- digest logger -->
	<logger name="api-digest-log" additivity="false">
		<level value="info"/>
		<appender-ref ref="api-digest-appender"/>
		<appender-ref ref="errors-appender"/>
	</logger>

	<!-- digest logger -->
	<logger name="sal-digest-log" additivity="false">
		<level value="info"/>
		<appender-ref ref="sal-digest-appender"/>
		<appender-ref ref="errors-appender"/>
	</logger>

	<!-- sql -->
	<logger name="java.sql" additivity="false">
		<level value="${log.level}" />
		<appender-ref ref="sql-appender" />
		<appender-ref ref="errors-appender" />
	</logger>

	<logger name="org.mybatis" additivity="false">
		<level value="${log.level}" />
		<appender-ref ref="sql-appender" />
		<appender-ref ref="errors-appender" />
	</logger>

	<logger name="com.alibaba.druid" additivity="false">
		<level value="INFO" />
		<appender-ref ref="druid-appender" />
	</logger>

	<!-- third -->
	<logger name="org.springframework" additivity="false">
		<level value="WARN" />
		<appender-ref ref="ROOT" />
		<appender-ref ref="errors-appender" />
	</logger>
	<logger name="org.apache" additivity="false">
		<level value="WARN" />
		<appender-ref ref="ROOT" />
		<appender-ref ref="errors-appender" />
	</logger>

	<logger name="springfox.documentation.spring.web.readers.parameter">
		<level value="INFO" />
		<appender-ref ref="ROOT" />
		<appender-ref ref="errors-appender" />
	</logger>
</included>
