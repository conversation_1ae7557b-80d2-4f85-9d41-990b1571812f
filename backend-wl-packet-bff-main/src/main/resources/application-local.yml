datasource:
  name: skeletonweb-ds
  driverClassName: com.mysql.jdbc.Driver
  url: **********************************************************************************************************************************
  username: rjkj
  password: zbt**++qq


spring:
  cloud:
    inetutils:
      preferred-networks: 192.168.100
  redis:
    host: ***************
    port: 7380
    timeout: 20000
    password: 1q2w3e4rasdf
    #    cluster:
    #      nodes: 127.0.0.1:7000,***************:7001,***************:7002
    #      maxRedirects: 6
    jedis:
      pool:
        max-active: 8
        min-idle: 0
        max-idle: 8
        max-wait: 1ms

feign:
  hystrix:
    enabled: true

management:
  endpoints:
    web:
      exposure:
        include:  '*'
  metrics:
    tags:
      application: ${spring.application.name}

#------------nacos-------------------------------------
nacos:
  host: ***************
  port: 8848
  url: ${nacos.host}:${nacos.port}
  namespace : 1511f407-9cb5-4aab-8a57-ba6c68947e62


##------------seata-------------------------------------
seata:
  application-id: ${spring.application.name}
  tx-service-group: ${spring.application.name}-group
  enable-auto-data-source-proxy: true
  config:
    type: nacos
    nacos:
      namespace: ${nacos.namespace}
      serverAddr: ${nacos.url}
      group: SEATA_GROUP
      userName: nacos
      password: nacos
  registry:
    type: nacos
    nacos:
      application: seata-server
      serverAddr: ${nacos.url}
      group: SEATA_GROUP
      namespace: ${nacos.namespace}
      userName: nacos
      password: nacos
      cluster: default
