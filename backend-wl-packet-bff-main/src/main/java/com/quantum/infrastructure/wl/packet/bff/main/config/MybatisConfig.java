package com.quantum.infrastructure.wl.packet.bff.main.config;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.context.annotation.Configuration;

/**
 * User: miracle
 * Date: 2020-03-23
 */
@Configuration
public class MybatisConfig implements BeanPostProcessor {

    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
        if(bean instanceof org.apache.ibatis.session.Configuration  ){
            org.apache.ibatis.session.Configuration configuration =  ( org.apache.ibatis.session.Configuration)bean ;
            configuration.setCallSettersOnNulls(true);
        }
        return bean;
    }
}