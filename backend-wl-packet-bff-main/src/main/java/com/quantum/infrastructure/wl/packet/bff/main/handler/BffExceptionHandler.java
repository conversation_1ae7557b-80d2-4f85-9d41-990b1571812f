package com.quantum.infrastructure.wl.packet.bff.main.handler;

import com.alibaba.fastjson.JSON;
import com.netflix.hystrix.exception.HystrixRuntimeException;
import com.quantum.infrastructure.common.util.exception.BusinessException;
import com.quantum.infrastructure.common.util.exception.FeignsException;
import com.quantum.infrastructure.common.util.protocol.CommonResultCode;
import com.quantum.infrastructure.common.util.protocol.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.validation.BindException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.stream.Collectors;

@Slf4j
@ControllerAdvice
public class BffExceptionHandler {

    /**
     * 处理自定义的业务异常
     *
     * @param req
     * @param e
     * @return
     */
    @ExceptionHandler(value = BusinessException.class)
    @ResponseBody
    public Result bizExceptionHandler(HttpServletRequest req, BusinessException e) {
        log.error("业务异常", e);
        Result result = new Result<>();
        if (e.getResultCode() != null) {
            result.setResultCode(e.getResultCode());
        } else {
            result.setResultCode(CommonResultCode.SYSTEM_ERR);
        }
        result.setMessage(e.getMessage());
        return result;
    }

    /**
     * 处理空指针的异常
     *
     * @param req
     * @param e
     * @return
     */
    @ExceptionHandler(value = NullPointerException.class)
    @ResponseBody
    public Result exceptionHandler(HttpServletRequest req, NullPointerException e) {
        log.error("空指针异常", e);
        Result result = new Result<>();
        result.setResultCode(CommonResultCode.SYSTEM_ERR);
        result.setMessage(e.getMessage());
        return result;
    }

    /**
     * 处理请求参数格式错误 @RequestBody上使用@Valid 实体上使用@NotNull等，验证失败后抛出的异常是MethodArgumentNotValidException异常
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseBody
    public Result MethodArgumentNotValidExceptionHandler(MethodArgumentNotValidException e) {
        log.error("sdk实体类注解异常", e);
        Result result = new Result<>();
        result.setResultCode(CommonResultCode.PARAM_ERR);
        result.setMessage(e.getBindingResult().getAllErrors().stream().map(DefaultMessageSourceResolvable::getDefaultMessage).collect(Collectors.joining()));
        return result;

    }

    @ExceptionHandler(BindException.class)
    public void BindExceptionHandler(HttpServletResponse response, BindException e) throws IOException {
        log.error("参数校验异常", e);
        Result result = new Result<>();
        result.setResultCode(CommonResultCode.PARAM_ERR);
        result.setMessage(e.getBindingResult().getAllErrors().get(0).getDefaultMessage());
        response.setCharacterEncoding("utf-8");
        response.setContentType("application/json; charset=utf-8");
        PrintWriter writer = response.getWriter();
        writer.write(JSON.toJSONString(result));
    }


    @ExceptionHandler(FeignsException.class)
    public void FeignsExceptionHandler(HttpServletResponse response, FeignsException e) throws IOException {
        log.error("服务异常", e);
        Result result = new Result<>();
        result.setResultCode(CommonResultCode.FEIGN_ERROR);
        result.setMessage(e.getMessage());
        response.setCharacterEncoding("utf-8");
        response.setContentType("application/json; charset=utf-8");
        PrintWriter writer = response.getWriter();
        writer.write(JSON.toJSONString(result));
    }

    @ExceptionHandler(HystrixRuntimeException.class)
    public void HystrixExceptionHandler(HttpServletResponse response, HystrixRuntimeException e) throws IOException {
        log.error("服务异常", e);
        Result result = new Result<>();
        result.setResultCode(CommonResultCode.FEIGN_ERROR);
        result.setMessage(e.getCause().getMessage());
        response.setCharacterEncoding("utf-8");
        response.setContentType("application/json; charset=utf-8");
        PrintWriter writer = response.getWriter();
        writer.write(JSON.toJSONString(result));
    }


    /**
     * 处理其他异常
     *
     * @param req
     * @param e
     * @return
     */
    @ExceptionHandler(value = Exception.class)
    @ResponseBody
    public Result exceptionHandler(HttpServletRequest req, Exception e) {
        log.error("程序异常", e);
        Result result = new Result<>();
        result.setResultCode(CommonResultCode.SYSTEM_ERR);
        result.setMessage(e.getMessage());
        return result;
    }
}