package com.quantum.infrastructure.wl.packet.bff.main.config;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Enumeration;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * User: miracle
 * Date: 2020-04-26
 */
@Configuration
public class FeginInterceptor implements RequestInterceptor {


    @Override
    public void apply(RequestTemplate requestTemplate) {
        try {
            Map<String, String> headers =   getHeaders();
            for (String headerName : headers.keySet()) {
                requestTemplate.header(headerName, headers.get(headerName));
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private Map<String, String> getHeaders() {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        Map<String, String> map = new LinkedHashMap<>();
        Enumeration<String> enumeration = request.getHeaderNames();
        while (enumeration.hasMoreElements()) {
            String key = enumeration.nextElement();
            String value = request.getHeader(key);
            if ("content-length".equals(key)) {
                continue;
            }
            map.put(key, value);

            if ("accept".equals(key)) {
                map.put("accept", "*/*");
            }else{
                map.put(key, value);
            }
        }

        String token = (String) request.getParameter("token");
        if (!StringUtils.isEmpty(token)) {
            map.put("token", token);
        }


        String tokenAttribute = (String) request.getAttribute("token");
        if (!StringUtils.isEmpty(tokenAttribute)) {
            map.put("token", tokenAttribute);
        }

        String timestamp = (String) request.getAttribute("timestamp");
        if (!StringUtils.isEmpty(timestamp)) {
            map.put("timestamp", timestamp);
        }

        String sign = (String) request.getAttribute("sign");
        if (!StringUtils.isEmpty(sign)) {
            map.put("sign", sign);
        }

        String key = (String) request.getAttribute("key");
        if (!StringUtils.isEmpty(key)) {
            map.put("key", key);
        }
        return map;
    }

}