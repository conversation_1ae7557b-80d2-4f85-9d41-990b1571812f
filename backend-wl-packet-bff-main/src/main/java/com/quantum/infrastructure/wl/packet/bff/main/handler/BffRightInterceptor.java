package com.quantum.infrastructure.wl.packet.bff.main.handler;

import com.quantum.infrastructure.common.auth.sdk.interceptor.SessionInterceptor;
import com.quantum.infrastructure.common.util.protocol.dto.SessionDto;
import com.quantum.infrastructure.common.util.utils.RedisUtils;
import com.quantum.infrastructure.wl.packet.bff.biz.cache.UserDao;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.annotation.Resource;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @ClassName BffInterceptor.java
 * @Description
 * @createTime 2023年05月12日 17:02:00
 */
@Component
public class BffRightInterceptor extends HandlerInterceptorAdapter {

    private static final Logger logger = LoggerFactory.getLogger(SessionInterceptor.class);
    public static final String SESSION_TOKEN = "token";
    @Resource(
            name = "redisTemplate"
    )
    private ValueOperations<String, SessionDto> valueOperations;
    @Resource(
            name = "redisTemplate"
    )
    private RedisTemplate<String, Object> redisTemplate;
    public static final String SESSION_KEY = "session:{token}";
    @Autowired
    RedisUtils redisUtils;

    @Autowired
    private UserDao userDao;

    public BffRightInterceptor() {
    }

    public static String getSessionKey(String token) {
        return "session:{token}".replaceAll("\\{token\\}", token);
    }

    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String token = request.getParameter("token");
        token = StringUtils.isEmpty(token) ? request.getHeader("token") : token;
        if (StringUtils.isEmpty(token)) {
            Cookie[] cookies = request.getCookies();
            if (cookies != null) {
                Cookie[] var6 = cookies;
                int var7 = cookies.length;

                for (int var8 = 0; var8 < var7; ++var8) {
                    Cookie cookie = var6[var8];
                    if (cookie.getName().equals("token")) {
                        token = cookie.getValue();
                        break;
                    }
                }
            }
        }


        if (!StringUtils.isEmpty(token)) {
            SessionDto sessionDto = null;
            try {
                sessionDto = (SessionDto) this.valueOperations.get(getSessionKey(token));
            } catch (Exception var15) {

            }

            if (sessionDto != null) {
                userDao.resetSessionInfo(token, sessionDto, 60 * 60 * 2);//时间设置为一小时
            }
        }

        return super.preHandle(request, response, handler);
    }


}
