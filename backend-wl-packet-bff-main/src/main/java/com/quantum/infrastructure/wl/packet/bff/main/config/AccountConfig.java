package com.quantum.infrastructure.wl.packet.bff.main.config;

import feign.Contract;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @Classname AccountConfig
 * @Date 2021/5/20 5:26 下午
 * @Created by miracle
 */
@Configuration
public class AccountConfig {

    @Bean
    public Contract feignContract() {
        return new HierarchicalContract();
    }
}
